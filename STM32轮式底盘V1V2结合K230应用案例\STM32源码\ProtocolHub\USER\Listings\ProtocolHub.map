Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.main) refers to bsp.o(i.BSP_init) for BSP_init
    main.o(i.main) refers to app_motor.o(i.Set_Motor) for Set_Motor
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to printfb.o(i.__0printf$bare) for __2printf
    main.o(i.main) refers to yb_protocol.o(i.Pto_Loop) for Pto_Loop
    main.o(i.main) refers to main.o(.data) for Car_Auto_Drive
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to bsp_usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to bsp_usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    bsp.o(i.BSP_init) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    bsp.o(i.BSP_init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    bsp.o(i.BSP_init) refers to delay.o(i.delay_init) for delay_init
    bsp.o(i.BSP_init) refers to bsp_usart.o(i.USART1_init) for USART1_init
    bsp.o(i.BSP_init) refers to bsp_usart.o(i.USART2_init) for USART2_init
    bsp.o(i.BSP_init) refers to bsp_rgb.o(i.RGB_Init) for RGB_Init
    bsp.o(i.BSP_init) refers to bsp_motor_iic.o(i.IIC_Motor_Init) for IIC_Motor_Init
    bsp.o(i.BSP_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp.o(i.BSP_init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    bsp_usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    bsp_usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    bsp_usart.o(i.USART1_IRQHandler) refers to bsp_usart.o(i.USART1_Send_U8) for USART1_Send_U8
    bsp_usart.o(i.USART1_Send_ArrayU8) refers to bsp_usart.o(i.USART1_Send_U8) for USART1_Send_U8
    bsp_usart.o(i.USART1_Send_U8) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bsp_usart.o(i.USART1_Send_U8) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    bsp_usart.o(i.USART1_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_usart.o(i.USART1_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_usart.o(i.USART1_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    bsp_usart.o(i.USART1_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    bsp_usart.o(i.USART1_init) refers to stm32f10x_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    bsp_usart.o(i.USART1_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    bsp_usart.o(i.USART1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    bsp_usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    bsp_usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    bsp_usart.o(i.USART2_IRQHandler) refers to yb_protocol.o(i.Pto_Data_Receive) for Pto_Data_Receive
    bsp_usart.o(i.USART2_IRQHandler) refers to yb_protocol.o(.data) for lost_count
    bsp_usart.o(i.USART2_Send_ArrayU8) refers to bsp_usart.o(i.USART2_Send_U8) for USART2_Send_U8
    bsp_usart.o(i.USART2_Send_U8) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bsp_usart.o(i.USART2_Send_U8) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    bsp_usart.o(i.USART2_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_usart.o(i.USART2_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_usart.o(i.USART2_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_usart.o(i.USART2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_usart.o(i.USART2_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    bsp_usart.o(i.USART2_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    bsp_usart.o(i.USART2_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    bsp_usart.o(i.fgetc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bsp_usart.o(i.fgetc) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    bsp_usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    bsp_usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bsp_motor_iic.o(i.IIC_Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_motor_iic.o(i.IIC_Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_motor_iic.o(i.Read_10_Enconder) refers to ioi2c.o(i.i2cRead) for i2cRead
    bsp_motor_iic.o(i.Read_10_Enconder) refers to bsp_motor_iic.o(.data) for buf
    bsp_motor_iic.o(i.Read_10_Enconder) refers to bsp_motor_iic.o(.bss) for Encoder_Offset
    bsp_motor_iic.o(i.Read_ALL_Enconder) refers to ioi2c.o(i.i2cRead) for i2cRead
    bsp_motor_iic.o(i.Read_ALL_Enconder) refers to bsp_motor_iic.o(.data) for buf
    bsp_motor_iic.o(i.Read_ALL_Enconder) refers to bsp_motor_iic.o(.bss) for Encoder_Now
    bsp_motor_iic.o(i.Set_Pluse_Phase) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_Pluse_Phase) refers to bsp_motor_iic.o(.data) for buf_tempPhase
    bsp_motor_iic.o(i.Set_Pluse_line) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_Pluse_line) refers to bsp_motor_iic.o(.data) for buf_templine
    bsp_motor_iic.o(i.Set_Wheel_dis) refers to bsp_motor_iic.o(i.float_to_bytes) for float_to_bytes
    bsp_motor_iic.o(i.Set_Wheel_dis) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_Wheel_dis) refers to bsp_motor_iic.o(.data) for bytes
    bsp_motor_iic.o(i.Set_motor_deadzone) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_motor_deadzone) refers to bsp_motor_iic.o(.data) for buf_tempzone
    bsp_motor_iic.o(i.Set_motor_type) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.char2float) refers to malloc.o(i.malloc) for malloc
    bsp_motor_iic.o(i.char2float) refers to malloc.o(i.free) for free
    bsp_motor_iic.o(i.control_pwm) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.control_pwm) refers to bsp_motor_iic.o(.data) for pwm
    bsp_motor_iic.o(i.control_speed) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.control_speed) refers to bsp_motor_iic.o(.data) for speed
    ioi2c.o(i.IIC_Ack) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_NAck) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Read_Byte) refers to ioi2c.o(i.IIC_Ack) for IIC_Ack
    ioi2c.o(i.IIC_Read_Byte) refers to ioi2c.o(i.IIC_NAck) for IIC_NAck
    ioi2c.o(i.IIC_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Start) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Wait_Ack) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Wait_Ack) refers to ioi2c.o(i.IIC_Stop) for IIC_Stop
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Start) for IIC_Start
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Send_Byte) for IIC_Send_Byte
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Stop) for IIC_Stop
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Read_Byte) for IIC_Read_Byte
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Start) for IIC_Start
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Send_Byte) for IIC_Send_Byte
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Stop) for IIC_Stop
    bsp_rgb.o(i.RGB_Clear) refers to bsp_rgb.o(.bss) for led_buf
    bsp_rgb.o(i.RGB_DMA_Init) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    bsp_rgb.o(i.RGB_DMA_Init) refers to stm32f10x_dma.o(i.DMA_DeInit) for DMA_DeInit
    bsp_rgb.o(i.RGB_DMA_Init) refers to stm32f10x_dma.o(i.DMA_Init) for DMA_Init
    bsp_rgb.o(i.RGB_DMA_Init) refers to bsp_rgb.o(.bss) for RGB_Byte_Buffer
    bsp_rgb.o(i.RGB_Driver_Init) refers to bsp_rgb.o(i.RGB_Spi_Init) for RGB_Spi_Init
    bsp_rgb.o(i.RGB_Driver_Init) refers to bsp_rgb.o(i.RGB_DMA_Init) for RGB_DMA_Init
    bsp_rgb.o(i.RGB_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_rgb.o(i.RGB_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_rgb.o(i.RGB_Init) refers to bsp_rgb.o(i.RGB_GPIO_Init) for RGB_GPIO_Init
    bsp_rgb.o(i.RGB_Init) refers to bsp_rgb.o(i.RGB_Driver_Init) for RGB_Driver_Init
    bsp_rgb.o(i.RGB_Set_Color) refers to bsp_rgb.o(.bss) for led_buf
    bsp_rgb.o(i.RGB_Set_Color_U32) refers to bsp_rgb.o(.bss) for led_buf
    bsp_rgb.o(i.RGB_Spi_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_rgb.o(i.RGB_Spi_Init) refers to stm32f10x_spi.o(i.SPI_Init) for SPI_Init
    bsp_rgb.o(i.RGB_Spi_Init) refers to stm32f10x_spi.o(i.SPI_Cmd) for SPI_Cmd
    bsp_rgb.o(i.RGB_Spi_Init) refers to stm32f10x_spi.o(i.SPI_I2S_DMACmd) for SPI_I2S_DMACmd
    bsp_rgb.o(i.RGB_Update) refers to stm32f10x_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    bsp_rgb.o(i.RGB_Update) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    bsp_rgb.o(i.RGB_Update) refers to stm32f10x_dma.o(i.DMA_GetFlagStatus) for DMA_GetFlagStatus
    bsp_rgb.o(i.RGB_Update) refers to stm32f10x_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    bsp_rgb.o(i.RGB_Update) refers to bsp_rgb.o(.bss) for led_buf
    yb_protocol.o(i.Get_LOST_Flag) refers to yb_protocol.o(.data) for lost_count
    yb_protocol.o(i.HandleAprilTag) refers to app_motor.o(i.AprilTag_Track) for AprilTag_Track
    yb_protocol.o(i.HandleColor) refers to app_motor.o(i.Visual_Line_Track) for Visual_Line_Track
    yb_protocol.o(i.HandleColor) refers to app_motor.o(i.Color_Trace) for Color_Trace
    yb_protocol.o(i.HandleColor) refers to main.o(.data) for color_mode
    yb_protocol.o(i.HandleEyeGaze) refers to app_motor.o(i.GazeDire_Track) for GazeDire_Track
    yb_protocol.o(i.HandleFaceDetect) refers to app_motor.o(i.Human_Face_Track) for Human_Face_Track
    yb_protocol.o(i.HandleFaceRecognition) refers to dflti.o(.text) for __aeabi_i2d
    yb_protocol.o(i.HandleFaceRecognition) refers to ddiv.o(.text) for __aeabi_ddiv
    yb_protocol.o(i.HandleFaceRecognition) refers to d2f.o(.text) for __aeabi_d2f
    yb_protocol.o(i.HandleFallDown) refers to dflti.o(.text) for __aeabi_i2d
    yb_protocol.o(i.HandleFallDown) refers to ddiv.o(.text) for __aeabi_ddiv
    yb_protocol.o(i.HandleFallDown) refers to d2f.o(.text) for __aeabi_d2f
    yb_protocol.o(i.HandleGuideDetect) refers to app_motor.o(i.RoadSign_Rec) for RoadSign_Rec
    yb_protocol.o(i.HandleHandDetect) refers to app_motor.o(i.Hand_Track) for Hand_Track
    yb_protocol.o(i.HandleLicenceDetect) refers to app_motor.o(i.Licence_Track) for Licence_Track
    yb_protocol.o(i.HandleMultiColorRec) refers to app_motor.o(i.Color_Rec) for Color_Rec
    yb_protocol.o(i.HandleNanoTracker) refers to app_motor.o(i.Target_Track) for Target_Track
    yb_protocol.o(i.HandleOCRRec) refers to app_motor.o(i.OCRrec_Actions) for OCRrec_Actions
    yb_protocol.o(i.HandleObstacleDetect) refers to app_motor.o(i.Autonomous_Avoid) for Autonomous_Avoid
    yb_protocol.o(i.HandlePersonDetect) refers to app_motor.o(i.HumanBody_Track) for HumanBody_Track
    yb_protocol.o(i.HandleQRCode) refers to app_motor.o(i.QRCode_Action) for QRCode_Action
    yb_protocol.o(i.HandleSelfLearning) refers to dflti.o(.text) for __aeabi_i2d
    yb_protocol.o(i.HandleSelfLearning) refers to ddiv.o(.text) for __aeabi_ddiv
    yb_protocol.o(i.HandleSelfLearning) refers to d2f.o(.text) for __aeabi_d2f
    yb_protocol.o(i.ParseCommonFields) refers to memcpya.o(.text) for __aeabi_memcpy
    yb_protocol.o(i.ParseCommonFields) refers to yb_protocol.o(i.Pto_Char_To_Int) for Pto_Char_To_Int
    yb_protocol.o(i.ParseCommonFields) refers to yb_protocol.o(.constdata) for func_table
    yb_protocol.o(i.Pto_Char_To_Int) refers to atoi.o(.text) for atoi
    yb_protocol.o(i.Pto_Clear_CMD_Flag) refers to yb_protocol.o(.bss) for RxBuffer
    yb_protocol.o(i.Pto_Clear_CMD_Flag) refers to yb_protocol.o(.data) for New_CMD_length
    yb_protocol.o(i.Pto_Data_Parse) refers to memseta.o(.text) for __aeabi_memclr4
    yb_protocol.o(i.Pto_Data_Parse) refers to yb_protocol.o(i.Pto_Char_To_Int) for Pto_Char_To_Int
    yb_protocol.o(i.Pto_Data_Parse) refers to yb_protocol.o(i.ParseCommonFields) for ParseCommonFields
    yb_protocol.o(i.Pto_Data_Receive) refers to yb_protocol.o(i.Pto_Clear_CMD_Flag) for Pto_Clear_CMD_Flag
    yb_protocol.o(i.Pto_Data_Receive) refers to yb_protocol.o(.data) for RxFlag
    yb_protocol.o(i.Pto_Data_Receive) refers to yb_protocol.o(.bss) for RxBuffer
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(i.Pto_Data_Parse) for Pto_Data_Parse
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(i.Pto_Clear_CMD_Flag) for Pto_Clear_CMD_Flag
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(i.Get_LOST_Flag) for Get_LOST_Flag
    yb_protocol.o(i.Pto_Loop) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(.data) for New_CMD_flag
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(.bss) for RxBuffer
    yb_protocol.o(i.Pto_Loop) refers to main.o(.data) for Car_Auto_Drive
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleColor) for HandleColor
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleBarcode) for HandleBarcode
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleQRCode) for HandleQRCode
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleAprilTag) for HandleAprilTag
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleDMCode) for HandleDMCode
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleFaceDetect) for HandleFaceDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleEyeGaze) for HandleEyeGaze
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleFaceRecognition) for HandleFaceRecognition
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandlePersonDetect) for HandlePersonDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleFallDown) for HandleFallDown
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleHandDetect) for HandleHandDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleHandGesture) for HandleHandGesture
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleOCRRec) for HandleOCRRec
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleObjectDetect) for HandleObjectDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleNanoTracker) for HandleNanoTracker
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleSelfLearning) for HandleSelfLearning
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleLicenceRec) for HandleLicenceRec
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleLicenceDetect) for HandleLicenceDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleGarbageDetect) for HandleGarbageDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleGuideDetect) for HandleGuideDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleObstacleDetect) for HandleObstacleDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleMultiColorRec) for HandleMultiColorRec
    app_motor.o(i.AprilTag_Track) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.AprilTag_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.AprilTag_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.AprilTag_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.AprilTag_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.AprilTag_Track) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.Autonomous_Avoid) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Autonomous_Avoid) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Autonomous_Avoid) refers to app_motor.o(.data) for target_area
    app_motor.o(i.Color_Rec) refers to strcmp.o(.text) for strcmp
    app_motor.o(i.Color_Rec) refers to bsp_rgb.o(i.RGB_Set_Color) for RGB_Set_Color
    app_motor.o(i.Color_Rec) refers to bsp_rgb.o(i.RGB_Update) for RGB_Update
    app_motor.o(i.Color_Rec) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Color_Trace) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Color_Trace) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Color_Trace) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.Color_Trace) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Color_Trace) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Color_Trace) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.GazeDire_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.GazeDire_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.GazeDire_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.GazeDire_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.GazeDire_Track) refers to app_motor.o(.data) for pid_output
    app_motor.o(i.Hand_Track) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Hand_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Hand_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.Hand_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Hand_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Hand_Track) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.HumanBody_Track) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.HumanBody_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.HumanBody_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.HumanBody_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.HumanBody_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.HumanBody_Track) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.Human_Face_Track) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Human_Face_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Human_Face_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.Human_Face_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Human_Face_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Human_Face_Track) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.Licence_Track) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Licence_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Licence_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.Licence_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Licence_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Licence_Track) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.Motion_Car_Control) refers to app_motor.o(i.Motion_Get_APB) for Motion_Get_APB
    app_motor.o(i.Motion_Car_Control) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Motion_Car_Control) refers to fdiv.o(.text) for __aeabi_fdiv
    app_motor.o(i.Motion_Car_Control) refers to fmul.o(.text) for __aeabi_fmul
    app_motor.o(i.Motion_Car_Control) refers to bsp_motor_iic.o(i.control_pwm) for control_pwm
    app_motor.o(i.Motion_Car_Control) refers to fadd.o(.text) for __aeabi_fsub
    app_motor.o(i.Motion_Car_Control) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Motion_Car_Control) refers to bsp_motor_iic.o(i.control_speed) for control_speed
    app_motor.o(i.Motion_Car_Control) refers to app_motor.o(.data) for speed_lr
    app_motor.o(i.OCRrec_Actions) refers to strcmp.o(.text) for strcmp
    app_motor.o(i.OCRrec_Actions) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.OCRrec_Actions) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.PID_Calc) refers to fadd.o(.text) for __aeabi_fadd
    app_motor.o(i.PID_Calc) refers to f2d.o(.text) for __aeabi_f2d
    app_motor.o(i.PID_Calc) refers to dmul.o(.text) for __aeabi_dmul
    app_motor.o(i.PID_Calc) refers to fmul.o(.text) for __aeabi_fmul
    app_motor.o(i.PID_Calc) refers to dadd.o(.text) for __aeabi_dadd
    app_motor.o(i.PID_Calc) refers to d2f.o(.text) for __aeabi_d2f
    app_motor.o(i.PID_Calc) refers to app_motor.o(.data) for Integral
    app_motor.o(i.QRCode_Action) refers to strcmp.o(.text) for strcmp
    app_motor.o(i.QRCode_Action) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.QRCode_Action) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.RoadSign_Rec) refers to strcmp.o(.text) for strcmp
    app_motor.o(i.RoadSign_Rec) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.RoadSign_Rec) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Set_Motor) refers to bsp_motor_iic.o(i.Set_motor_type) for Set_motor_type
    app_motor.o(i.Set_Motor) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Set_Motor) refers to bsp_motor_iic.o(i.Set_Pluse_Phase) for Set_Pluse_Phase
    app_motor.o(i.Set_Motor) refers to bsp_motor_iic.o(i.Set_Pluse_line) for Set_Pluse_line
    app_motor.o(i.Set_Motor) refers to bsp_motor_iic.o(i.Set_Wheel_dis) for Set_Wheel_dis
    app_motor.o(i.Set_Motor) refers to bsp_motor_iic.o(i.Set_motor_deadzone) for Set_motor_deadzone
    app_motor.o(i.Set_Motor) refers to app_motor.o(.data) for PWM_Car_Flag
    app_motor.o(i.Target_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Target_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.Target_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Target_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Target_Track) refers to app_motor.o(.data) for pid_output
    app_motor.o(i.Visual_Line_Track) refers to delay.o(i.delay_ms) for delay_ms
    app_motor.o(i.Visual_Line_Track) refers to fflti.o(.text) for __aeabi_i2f
    app_motor.o(i.Visual_Line_Track) refers to app_motor.o(i.PID_Calc) for PID_Calc
    app_motor.o(i.Visual_Line_Track) refers to ffixi.o(.text) for __aeabi_f2iz
    app_motor.o(i.Visual_Line_Track) refers to app_motor.o(i.Motion_Car_Control) for Motion_Car_Control
    app_motor.o(i.Visual_Line_Track) refers to app_motor.o(.data) for Wait_Flag
    app_motor.o(i.Visual_Line_Track) refers to yb_protocol.o(.data) for w
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to bsp_usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to bsp_usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to bsp_usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to bsp_usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to bsp_usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to bsp_usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to bsp_usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to bsp_usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to bsp_usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to bsp_usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to bsp_usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to bsp_usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to bsp_usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to bsp_usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to bsp_usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to bsp_usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to bsp_usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to bsp_usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to bsp_usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to bsp_usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to bsp_usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to bsp_usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to bsp_usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to bsp_usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to bsp_usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to bsp_usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to bsp_usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to bsp_usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to bsp_usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to bsp_usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to bsp_usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to bsp_usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to bsp_usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to bsp_usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to bsp_usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to bsp_usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to bsp_usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to bsp_usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to bsp_usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to bsp_usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to bsp_usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to bsp_usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to bsp_usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to bsp_usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing bsp_usart.o(i.USART1_Send_ArrayU8), (28 bytes).
    Removing bsp_usart.o(i.USART2_Send_ArrayU8), (28 bytes).
    Removing bsp_usart.o(i.USART2_Send_U8), (32 bytes).
    Removing bsp_usart.o(i.fgetc), (32 bytes).
    Removing bsp_motor_iic.o(i.Read_10_Enconder), (140 bytes).
    Removing bsp_motor_iic.o(i.Read_ALL_Enconder), (240 bytes).
    Removing bsp_motor_iic.o(i.char2float), (28 bytes).
    Removing bsp_motor_iic.o(.bss), (32 bytes).
    Removing ioi2c.o(i.IIC_Ack), (76 bytes).
    Removing ioi2c.o(i.IIC_NAck), (76 bytes).
    Removing ioi2c.o(i.IIC_Read_Byte), (112 bytes).
    Removing ioi2c.o(i.i2cRead), (118 bytes).
    Removing bsp_rgb.o(i.RGB_Clear), (28 bytes).
    Removing bsp_rgb.o(i.RGB_Set_Color_U32), (44 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

464 unused section(s) (total 19008 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ..\APP\app_motor.c                       0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\APP\yb_protocol.c                     0x00000000   Number         0  yb_protocol.o ABSOLUTE
    ..\BSP\Motor\IOI2C.c                     0x00000000   Number         0  ioi2c.o ABSOLUTE
    ..\BSP\Motor\bsp_motor_iic.c             0x00000000   Number         0  bsp_motor_iic.o ABSOLUTE
    ..\BSP\RGB\bsp_rgb.c                     0x00000000   Number         0  bsp_rgb.o ABSOLUTE
    ..\BSP\UART\bsp_usart.c                  0x00000000   Number         0  bsp_usart.o ABSOLUTE
    ..\BSP\bsp.c                             0x00000000   Number         0  bsp.o ABSOLUTE
    ..\BSP\delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\FWLib\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_adc.c             0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_bkp.c             0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\FWLib\src\stm32f10x_can.c             0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\FWLib\src\stm32f10x_cec.c             0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\FWLib\src\stm32f10x_crc.c             0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_dac.c             0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\FWLib\src\stm32f10x_dbgmcu.c          0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\FWLib\src\stm32f10x_dma.c             0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\FWLib\src\stm32f10x_exti.c            0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\FWLib\src\stm32f10x_flash.c           0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\FWLib\src\stm32f10x_fsmc.c            0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_gpio.c            0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\FWLib\src\stm32f10x_i2c.c             0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\FWLib\src\stm32f10x_iwdg.c            0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\FWLib\src\stm32f10x_pwr.c             0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\FWLib\src\stm32f10x_rcc.c             0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_rtc.c             0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_sdio.c            0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\FWLib\src\stm32f10x_spi.c             0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\FWLib\src\stm32f10x_tim.c             0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\FWLib\src\stm32f10x_usart.c           0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\FWLib\src\stm32f10x_wwdg.c            0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section        0  memcpya.o(.text)
    .text                                    0x0800018c   Section        0  memseta.o(.text)
    .text                                    0x080001b0   Section        0  strcmp.o(.text)
    .text                                    0x080001cc   Section        0  atoi.o(.text)
    .text                                    0x080001e6   Section        0  fadd.o(.text)
    .text                                    0x08000296   Section        0  fmul.o(.text)
    .text                                    0x080002fa   Section        0  fdiv.o(.text)
    .text                                    0x08000376   Section        0  dadd.o(.text)
    .text                                    0x080004c4   Section        0  dmul.o(.text)
    .text                                    0x080005a8   Section        0  ddiv.o(.text)
    .text                                    0x08000686   Section        0  fflti.o(.text)
    .text                                    0x08000698   Section        0  dflti.o(.text)
    .text                                    0x080006ba   Section        0  ffixi.o(.text)
    .text                                    0x080006ec   Section        0  f2d.o(.text)
    .text                                    0x08000712   Section        0  d2f.o(.text)
    .text                                    0x0800074a   Section        0  llshl.o(.text)
    .text                                    0x08000768   Section        0  llsshr.o(.text)
    .text                                    0x0800078c   Section        0  strtol.o(.text)
    .text                                    0x080007fc   Section        0  iusefp.o(.text)
    .text                                    0x080007fc   Section        0  fepilogue.o(.text)
    .text                                    0x0800086a   Section        0  depilogue.o(.text)
    .text                                    0x08000924   Section       36  init.o(.text)
    .text                                    0x08000948   Section        0  llushr.o(.text)
    .text                                    0x08000968   Section        0  ctype_o.o(.text)
    .text                                    0x08000970   Section        0  _strtoul.o(.text)
    .text                                    0x08000a0e   Section        0  _chval.o(.text)
    i.AprilTag_Track                         0x08000a2c   Section        0  app_motor.o(i.AprilTag_Track)
    i.Autonomous_Avoid                       0x08000b20   Section        0  app_motor.o(i.Autonomous_Avoid)
    i.BSP_init                               0x08000b54   Section        0  bsp.o(i.BSP_init)
    i.BusFault_Handler                       0x08000b94   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Color_Rec                              0x08000b98   Section        0  app_motor.o(i.Color_Rec)
    i.Color_Trace                            0x08000c1c   Section        0  app_motor.o(i.Color_Trace)
    i.DMA_ClearFlag                          0x08000d30   Section        0  stm32f10x_dma.o(i.DMA_ClearFlag)
    i.DMA_Cmd                                0x08000d4c   Section        0  stm32f10x_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x08000d64   Section        0  stm32f10x_dma.o(i.DMA_DeInit)
    i.DMA_GetFlagStatus                      0x08000eb0   Section        0  stm32f10x_dma.o(i.DMA_GetFlagStatus)
    i.DMA_Init                               0x08000edc   Section        0  stm32f10x_dma.o(i.DMA_Init)
    i.DMA_SetCurrDataCounter                 0x08000f18   Section        0  stm32f10x_dma.o(i.DMA_SetCurrDataCounter)
    i.DebugMon_Handler                       0x08000f1c   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08000f1e   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x08001034   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GazeDire_Track                         0x080010c4   Section        0  app_motor.o(i.GazeDire_Track)
    i.Get_LOST_Flag                          0x08001164   Section        0  yb_protocol.o(i.Get_LOST_Flag)
    i.Hand_Track                             0x08001198   Section        0  app_motor.o(i.Hand_Track)
    i.HandleAprilTag                         0x080012b0   Section        0  yb_protocol.o(i.HandleAprilTag)
    i.HandleBarcode                          0x080012d8   Section        0  yb_protocol.o(i.HandleBarcode)
    i.HandleColor                            0x080012e8   Section        0  yb_protocol.o(i.HandleColor)
    i.HandleDMCode                           0x08001328   Section        0  yb_protocol.o(i.HandleDMCode)
    i.HandleEyeGaze                          0x0800133a   Section        0  yb_protocol.o(i.HandleEyeGaze)
    i.HandleFaceDetect                       0x08001358   Section        0  yb_protocol.o(i.HandleFaceDetect)
    i.HandleFaceRecognition                  0x08001378   Section        0  yb_protocol.o(i.HandleFaceRecognition)
    i.HandleFallDown                         0x080013b8   Section        0  yb_protocol.o(i.HandleFallDown)
    i.HandleGarbageDetect                    0x080013f8   Section        0  yb_protocol.o(i.HandleGarbageDetect)
    i.HandleGuideDetect                      0x08001408   Section        0  yb_protocol.o(i.HandleGuideDetect)
    i.HandleHandDetect                       0x0800142c   Section        0  yb_protocol.o(i.HandleHandDetect)
    i.HandleHandGesture                      0x0800144c   Section        0  yb_protocol.o(i.HandleHandGesture)
    i.HandleLicenceDetect                    0x08001452   Section        0  yb_protocol.o(i.HandleLicenceDetect)
    i.HandleLicenceRec                       0x08001486   Section        0  yb_protocol.o(i.HandleLicenceRec)
    i.HandleMultiColorRec                    0x0800148c   Section        0  yb_protocol.o(i.HandleMultiColorRec)
    i.HandleNanoTracker                      0x080014b0   Section        0  yb_protocol.o(i.HandleNanoTracker)
    i.HandleOCRRec                           0x080014cc   Section        0  yb_protocol.o(i.HandleOCRRec)
    i.HandleObjectDetect                     0x080014dc   Section        0  yb_protocol.o(i.HandleObjectDetect)
    i.HandleObstacleDetect                   0x080014ec   Section        0  yb_protocol.o(i.HandleObstacleDetect)
    i.HandlePersonDetect                     0x08001510   Section        0  yb_protocol.o(i.HandlePersonDetect)
    i.HandleQRCode                           0x08001530   Section        0  yb_protocol.o(i.HandleQRCode)
    i.HandleSelfLearning                     0x08001554   Section        0  yb_protocol.o(i.HandleSelfLearning)
    i.HardFault_Handler                      0x08001580   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.HumanBody_Track                        0x08001584   Section        0  app_motor.o(i.HumanBody_Track)
    i.Human_Face_Track                       0x08001698   Section        0  app_motor.o(i.Human_Face_Track)
    i.IIC_Motor_Init                         0x0800178c   Section        0  bsp_motor_iic.o(i.IIC_Motor_Init)
    i.IIC_Send_Byte                          0x080017b8   Section        0  ioi2c.o(i.IIC_Send_Byte)
    i.IIC_Start                              0x0800181c   Section        0  ioi2c.o(i.IIC_Start)
    i.IIC_Stop                               0x08001884   Section        0  ioi2c.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x080018cc   Section        0  ioi2c.o(i.IIC_Wait_Ack)
    i.Licence_Track                          0x08001934   Section        0  app_motor.o(i.Licence_Track)
    i.MemManage_Handler                      0x08001a64   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motion_Car_Control                     0x08001a68   Section        0  app_motor.o(i.Motion_Car_Control)
    i.Motion_Get_APB                         0x08001c04   Section        0  app_motor.o(i.Motion_Get_APB)
    Motion_Get_APB                           0x08001c05   Thumb Code     4  app_motor.o(i.Motion_Get_APB)
    i.NMI_Handler                            0x08001c0c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001c10   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001c80   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OCRrec_Actions                         0x08001c94   Section        0  app_motor.o(i.OCRrec_Actions)
    i.PID_Calc                               0x08001e7c   Section        0  app_motor.o(i.PID_Calc)
    i.ParseCommonFields                      0x08001f10   Section        0  yb_protocol.o(i.ParseCommonFields)
    i.PendSV_Handler                         0x08001fd0   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.Pto_Char_To_Int                        0x08001fd2   Section        0  yb_protocol.o(i.Pto_Char_To_Int)
    i.Pto_Clear_CMD_Flag                     0x08001fe0   Section        0  yb_protocol.o(i.Pto_Clear_CMD_Flag)
    i.Pto_Data_Parse                         0x0800200c   Section        0  yb_protocol.o(i.Pto_Data_Parse)
    i.Pto_Data_Receive                       0x080020b0   Section        0  yb_protocol.o(i.Pto_Data_Receive)
    i.Pto_Loop                               0x08002140   Section        0  yb_protocol.o(i.Pto_Loop)
    i.QRCode_Action                          0x080021a0   Section        0  app_motor.o(i.QRCode_Action)
    i.RCC_AHBPeriphClockCmd                  0x0800238c   Section        0  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x080023ac   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080023cc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080023ec   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.RGB_DMA_Init                           0x080024c0   Section        0  bsp_rgb.o(i.RGB_DMA_Init)
    RGB_DMA_Init                             0x080024c1   Thumb Code    70  bsp_rgb.o(i.RGB_DMA_Init)
    i.RGB_Driver_Init                        0x08002514   Section        0  bsp_rgb.o(i.RGB_Driver_Init)
    RGB_Driver_Init                          0x08002515   Thumb Code    12  bsp_rgb.o(i.RGB_Driver_Init)
    i.RGB_GPIO_Init                          0x08002520   Section        0  bsp_rgb.o(i.RGB_GPIO_Init)
    RGB_GPIO_Init                            0x08002521   Thumb Code    38  bsp_rgb.o(i.RGB_GPIO_Init)
    i.RGB_Init                               0x0800254c   Section        0  bsp_rgb.o(i.RGB_Init)
    i.RGB_Set_Color                          0x08002558   Section        0  bsp_rgb.o(i.RGB_Set_Color)
    i.RGB_Spi_Init                           0x08002590   Section        0  bsp_rgb.o(i.RGB_Spi_Init)
    RGB_Spi_Init                             0x08002591   Thumb Code    98  bsp_rgb.o(i.RGB_Spi_Init)
    i.RGB_Update                             0x080025f8   Section        0  bsp_rgb.o(i.RGB_Update)
    i.RoadSign_Rec                           0x08002678   Section        0  app_motor.o(i.RoadSign_Rec)
    i.SPI_Cmd                                0x08002744   Section        0  stm32f10x_spi.o(i.SPI_Cmd)
    i.SPI_I2S_DMACmd                         0x0800275c   Section        0  stm32f10x_spi.o(i.SPI_I2S_DMACmd)
    i.SPI_Init                               0x0800276e   Section        0  stm32f10x_spi.o(i.SPI_Init)
    i.SVC_Handler                            0x080027aa   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080027ac   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080027ad   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080027b4   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080027b5   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.Set_Motor                              0x08002894   Section        0  app_motor.o(i.Set_Motor)
    i.Set_Pluse_Phase                        0x080029e8   Section        0  bsp_motor_iic.o(i.Set_Pluse_Phase)
    i.Set_Pluse_line                         0x08002a08   Section        0  bsp_motor_iic.o(i.Set_Pluse_line)
    i.Set_Wheel_dis                          0x08002a28   Section        0  bsp_motor_iic.o(i.Set_Wheel_dis)
    i.Set_motor_deadzone                     0x08002a48   Section        0  bsp_motor_iic.o(i.Set_motor_deadzone)
    i.Set_motor_type                         0x08002a68   Section        0  bsp_motor_iic.o(i.Set_motor_type)
    i.SysTick_CLKSourceConfig                0x08002a78   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08002aa0   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08002aa4   Section        0  system_stm32f10x.o(i.SystemInit)
    i.Target_Track                           0x08002b04   Section        0  app_motor.o(i.Target_Track)
    i.USART1_IRQHandler                      0x08002b6c   Section        0  bsp_usart.o(i.USART1_IRQHandler)
    i.USART1_Send_U8                         0x08002b90   Section        0  bsp_usart.o(i.USART1_Send_U8)
    i.USART1_init                            0x08002bb0   Section        0  bsp_usart.o(i.USART1_init)
    i.USART2_IRQHandler                      0x08002c68   Section        0  bsp_usart.o(i.USART2_IRQHandler)
    i.USART2_init                            0x08002ca0   Section        0  bsp_usart.o(i.USART2_init)
    i.USART_ClearFlag                        0x08002d40   Section        0  stm32f10x_usart.o(i.USART_ClearFlag)
    i.USART_ClearITPendingBit                0x08002d52   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08002d70   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08002d88   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08002da2   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08002df6   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08002e40   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08002f18   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08002f22   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08002f2a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.Visual_Line_Track                      0x08002f30   Section        0  app_motor.o(i.Visual_Line_Track)
    i.__0printf$bare                         0x08002fb4   Section        0  printfb.o(i.__0printf$bare)
    i.__aeabi_errno_addr                     0x08002fc4   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__scatterload_copy                     0x08002fcc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002fda   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002fdc   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08002fea   Section        0  printfb.o(i._printf_core)
    _printf_core                             0x08002feb   Thumb Code    34  printfb.o(i._printf_core)
    i.control_pwm                            0x0800300c   Section        0  bsp_motor_iic.o(i.control_pwm)
    i.control_speed                          0x08003048   Section        0  bsp_motor_iic.o(i.control_speed)
    i.delay_init                             0x08003084   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080030d0   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x0800310c   Section        0  delay.o(i.delay_us)
    i.float_to_bytes                         0x08003148   Section        0  bsp_motor_iic.o(i.float_to_bytes)
    i.fputc                                  0x0800314c   Section        0  bsp_usart.o(i.fputc)
    i.i2cWrite                               0x08003170   Section        0  ioi2c.o(i.i2cWrite)
    i.main                                   0x080031d0   Section        0  main.o(i.main)
    .constdata                               0x08003258   Section      544  yb_protocol.o(.constdata)
    color_fields                             0x08003258   Data           8  yb_protocol.o(.constdata)
    barcode_fields                           0x08003260   Data          10  yb_protocol.o(.constdata)
    qrcode_fields                            0x0800326a   Data          10  yb_protocol.o(.constdata)
    apriltag_fields                          0x08003274   Data          12  yb_protocol.o(.constdata)
    dmcode_fields                            0x08003280   Data          12  yb_protocol.o(.constdata)
    facedetect_fields                        0x0800328c   Data           8  yb_protocol.o(.constdata)
    eyegaze_fields                           0x08003294   Data           8  yb_protocol.o(.constdata)
    facerecog_fields                         0x0800329c   Data          12  yb_protocol.o(.constdata)
    persondetect_fields                      0x080032a8   Data           8  yb_protocol.o(.constdata)
    falldown_fields                          0x080032b0   Data          12  yb_protocol.o(.constdata)
    handdetect_fields                        0x080032bc   Data           8  yb_protocol.o(.constdata)
    handgesture_fields                       0x080032c4   Data           2  yb_protocol.o(.constdata)
    ocrrec_fields                            0x080032c6   Data           2  yb_protocol.o(.constdata)
    objectdetect_fields                      0x080032c8   Data          10  yb_protocol.o(.constdata)
    nanotracker_fields                       0x080032d2   Data           8  yb_protocol.o(.constdata)
    selflearning_fields                      0x080032da   Data           4  yb_protocol.o(.constdata)
    licencerec_fields                        0x080032de   Data           2  yb_protocol.o(.constdata)
    licencedetect_fields                     0x080032e0   Data          16  yb_protocol.o(.constdata)
    garbagedetect_fields                     0x080032f0   Data          10  yb_protocol.o(.constdata)
    guidedetect_fields                       0x080032fa   Data          10  yb_protocol.o(.constdata)
    obstacledetect_fields                    0x08003304   Data          10  yb_protocol.o(.constdata)
    multicolorrec_fields                     0x0800330e   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08003478   Section      129  ctype_o.o(.constdata)
    .constdata                               0x080034fc   Section        4  ctype_o.o(.constdata)
    table                                    0x080034fc   Data           4  ctype_o.o(.constdata)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f10x.o(.data)
    .data                                    0x2000001c   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000001c   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000002c   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000030   Section        4  delay.o(.data)
    fac_us                                   0x20000030   Data           1  delay.o(.data)
    fac_ms                                   0x20000032   Data           2  delay.o(.data)
    .data                                    0x20000034   Section       32  bsp_motor_iic.o(.data)
    buf_tempzone                             0x20000034   Data           2  bsp_motor_iic.o(.data)
    buf_templine                             0x20000036   Data           2  bsp_motor_iic.o(.data)
    buf_tempPhase                            0x20000038   Data           2  bsp_motor_iic.o(.data)
    bytes                                    0x2000003a   Data           4  bsp_motor_iic.o(.data)
    speed                                    0x2000003e   Data           8  bsp_motor_iic.o(.data)
    pwm                                      0x20000046   Data           8  bsp_motor_iic.o(.data)
    buf                                      0x2000004e   Data           2  bsp_motor_iic.o(.data)
    buf                                      0x20000050   Data           2  bsp_motor_iic.o(.data)
    buf2                                     0x20000052   Data           2  bsp_motor_iic.o(.data)
    .data                                    0x20000054   Section       56  yb_protocol.o(.data)
    .data                                    0x2000008c   Section       60  app_motor.o(.data)
    speed_lr                                 0x200000a4   Data           4  app_motor.o(.data)
    speed_fb                                 0x200000a8   Data           4  app_motor.o(.data)
    speed_spin                               0x200000ac   Data           4  app_motor.o(.data)
    speed_L1_setup                           0x200000b0   Data           4  app_motor.o(.data)
    speed_L2_setup                           0x200000b4   Data           4  app_motor.o(.data)
    speed_R1_setup                           0x200000b8   Data           4  app_motor.o(.data)
    speed_R2_setup                           0x200000bc   Data           4  app_motor.o(.data)
    error_last                               0x200000c0   Data           4  app_motor.o(.data)
    Integral                                 0x200000c4   Data           4  app_motor.o(.data)
    .data                                    0x200000c8   Section        4  stdout.o(.data)
    .data                                    0x200000cc   Section        4  errno.o(.data)
    _errno                                   0x200000cc   Data           4  errno.o(.data)
    .bss                                     0x200000d0   Section      394  bsp_rgb.o(.bss)
    .bss                                     0x2000025a   Section       50  yb_protocol.o(.bss)
    STACK                                    0x20000290   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_memcpy                           0x08000169   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000169   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000169   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800018d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800018d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800018d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800019b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800019b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800019b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800019f   Thumb Code    18  memseta.o(.text)
    strcmp                                   0x080001b1   Thumb Code    28  strcmp.o(.text)
    atoi                                     0x080001cd   Thumb Code    26  atoi.o(.text)
    __aeabi_fadd                             0x080001e7   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x0800028b   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08000291   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x08000297   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x080002fb   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x08000377   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080004b9   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080004bf   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080004c5   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080005a9   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2f                              0x08000687   Thumb Code    18  fflti.o(.text)
    __aeabi_i2d                              0x08000699   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x080006bb   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2d                              0x080006ed   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000713   Thumb Code    56  d2f.o(.text)
    __aeabi_llsl                             0x0800074b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800074b   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000769   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000769   Thumb Code     0  llsshr.o(.text)
    strtol                                   0x0800078d   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x080007fd   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080007fd   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800080f   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800086b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000889   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x08000925   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000925   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x08000949   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000949   Thumb Code     0  llushr.o(.text)
    __rt_ctype_table                         0x08000969   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x08000971   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x08000a0f   Thumb Code    28  _chval.o(.text)
    AprilTag_Track                           0x08000a2d   Thumb Code   220  app_motor.o(i.AprilTag_Track)
    Autonomous_Avoid                         0x08000b21   Thumb Code    48  app_motor.o(i.Autonomous_Avoid)
    BSP_init                                 0x08000b55   Thumb Code    60  bsp.o(i.BSP_init)
    BusFault_Handler                         0x08000b95   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Color_Rec                                0x08000b99   Thumb Code   112  app_motor.o(i.Color_Rec)
    Color_Trace                              0x08000c1d   Thumb Code   250  app_motor.o(i.Color_Trace)
    DMA_ClearFlag                            0x08000d31   Thumb Code    18  stm32f10x_dma.o(i.DMA_ClearFlag)
    DMA_Cmd                                  0x08000d4d   Thumb Code    24  stm32f10x_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x08000d65   Thumb Code   324  stm32f10x_dma.o(i.DMA_DeInit)
    DMA_GetFlagStatus                        0x08000eb1   Thumb Code    36  stm32f10x_dma.o(i.DMA_GetFlagStatus)
    DMA_Init                                 0x08000edd   Thumb Code    60  stm32f10x_dma.o(i.DMA_Init)
    DMA_SetCurrDataCounter                   0x08000f19   Thumb Code     4  stm32f10x_dma.o(i.DMA_SetCurrDataCounter)
    DebugMon_Handler                         0x08000f1d   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08000f1f   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x08001035   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GazeDire_Track                           0x080010c5   Thumb Code   156  app_motor.o(i.GazeDire_Track)
    Get_LOST_Flag                            0x08001165   Thumb Code    44  yb_protocol.o(i.Get_LOST_Flag)
    Hand_Track                               0x08001199   Thumb Code   254  app_motor.o(i.Hand_Track)
    HandleAprilTag                           0x080012b1   Thumb Code    40  yb_protocol.o(i.HandleAprilTag)
    HandleBarcode                            0x080012d9   Thumb Code    16  yb_protocol.o(i.HandleBarcode)
    HandleColor                              0x080012e9   Thumb Code    58  yb_protocol.o(i.HandleColor)
    HandleDMCode                             0x08001329   Thumb Code    18  yb_protocol.o(i.HandleDMCode)
    HandleEyeGaze                            0x0800133b   Thumb Code    30  yb_protocol.o(i.HandleEyeGaze)
    HandleFaceDetect                         0x08001359   Thumb Code    32  yb_protocol.o(i.HandleFaceDetect)
    HandleFaceRecognition                    0x08001379   Thumb Code    58  yb_protocol.o(i.HandleFaceRecognition)
    HandleFallDown                           0x080013b9   Thumb Code    58  yb_protocol.o(i.HandleFallDown)
    HandleGarbageDetect                      0x080013f9   Thumb Code    16  yb_protocol.o(i.HandleGarbageDetect)
    HandleGuideDetect                        0x08001409   Thumb Code    36  yb_protocol.o(i.HandleGuideDetect)
    HandleHandDetect                         0x0800142d   Thumb Code    32  yb_protocol.o(i.HandleHandDetect)
    HandleHandGesture                        0x0800144d   Thumb Code     6  yb_protocol.o(i.HandleHandGesture)
    HandleLicenceDetect                      0x08001453   Thumb Code    52  yb_protocol.o(i.HandleLicenceDetect)
    HandleLicenceRec                         0x08001487   Thumb Code     6  yb_protocol.o(i.HandleLicenceRec)
    HandleMultiColorRec                      0x0800148d   Thumb Code    36  yb_protocol.o(i.HandleMultiColorRec)
    HandleNanoTracker                        0x080014b1   Thumb Code    28  yb_protocol.o(i.HandleNanoTracker)
    HandleOCRRec                             0x080014cd   Thumb Code    16  yb_protocol.o(i.HandleOCRRec)
    HandleObjectDetect                       0x080014dd   Thumb Code    16  yb_protocol.o(i.HandleObjectDetect)
    HandleObstacleDetect                     0x080014ed   Thumb Code    36  yb_protocol.o(i.HandleObstacleDetect)
    HandlePersonDetect                       0x08001511   Thumb Code    32  yb_protocol.o(i.HandlePersonDetect)
    HandleQRCode                             0x08001531   Thumb Code    36  yb_protocol.o(i.HandleQRCode)
    HandleSelfLearning                       0x08001555   Thumb Code    38  yb_protocol.o(i.HandleSelfLearning)
    HardFault_Handler                        0x08001581   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    HumanBody_Track                          0x08001585   Thumb Code   248  app_motor.o(i.HumanBody_Track)
    Human_Face_Track                         0x08001699   Thumb Code   220  app_motor.o(i.Human_Face_Track)
    IIC_Motor_Init                           0x0800178d   Thumb Code    40  bsp_motor_iic.o(i.IIC_Motor_Init)
    IIC_Send_Byte                            0x080017b9   Thumb Code    88  ioi2c.o(i.IIC_Send_Byte)
    IIC_Start                                0x0800181d   Thumb Code    92  ioi2c.o(i.IIC_Start)
    IIC_Stop                                 0x08001885   Thumb Code    60  ioi2c.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x080018cd   Thumb Code    92  ioi2c.o(i.IIC_Wait_Ack)
    Licence_Track                            0x08001935   Thumb Code   280  app_motor.o(i.Licence_Track)
    MemManage_Handler                        0x08001a65   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    Motion_Car_Control                       0x08001a69   Thumb Code   372  app_motor.o(i.Motion_Car_Control)
    NMI_Handler                              0x08001c0d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001c11   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001c81   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OCRrec_Actions                           0x08001c95   Thumb Code   358  app_motor.o(i.OCRrec_Actions)
    PID_Calc                                 0x08001e7d   Thumb Code   126  app_motor.o(i.PID_Calc)
    ParseCommonFields                        0x08001f11   Thumb Code   188  yb_protocol.o(i.ParseCommonFields)
    PendSV_Handler                           0x08001fd1   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    Pto_Char_To_Int                          0x08001fd3   Thumb Code    12  yb_protocol.o(i.Pto_Char_To_Int)
    Pto_Clear_CMD_Flag                       0x08001fe1   Thumb Code    30  yb_protocol.o(i.Pto_Clear_CMD_Flag)
    Pto_Data_Parse                           0x0800200d   Thumb Code   162  yb_protocol.o(i.Pto_Data_Parse)
    Pto_Data_Receive                         0x080020b1   Thumb Code   124  yb_protocol.o(i.Pto_Data_Receive)
    Pto_Loop                                 0x08002141   Thumb Code    74  yb_protocol.o(i.Pto_Loop)
    QRCode_Action                            0x080021a1   Thumb Code   360  app_motor.o(i.QRCode_Action)
    RCC_AHBPeriphClockCmd                    0x0800238d   Thumb Code    26  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x080023ad   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080023cd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080023ed   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    RGB_Init                                 0x0800254d   Thumb Code    12  bsp_rgb.o(i.RGB_Init)
    RGB_Set_Color                            0x08002559   Thumb Code    52  bsp_rgb.o(i.RGB_Set_Color)
    RGB_Update                               0x080025f9   Thumb Code   114  bsp_rgb.o(i.RGB_Update)
    RoadSign_Rec                             0x08002679   Thumb Code   156  app_motor.o(i.RoadSign_Rec)
    SPI_Cmd                                  0x08002745   Thumb Code    24  stm32f10x_spi.o(i.SPI_Cmd)
    SPI_I2S_DMACmd                           0x0800275d   Thumb Code    18  stm32f10x_spi.o(i.SPI_I2S_DMACmd)
    SPI_Init                                 0x0800276f   Thumb Code    60  stm32f10x_spi.o(i.SPI_Init)
    SVC_Handler                              0x080027ab   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Set_Motor                                0x08002895   Thumb Code   324  app_motor.o(i.Set_Motor)
    Set_Pluse_Phase                          0x080029e9   Thumb Code    26  bsp_motor_iic.o(i.Set_Pluse_Phase)
    Set_Pluse_line                           0x08002a09   Thumb Code    26  bsp_motor_iic.o(i.Set_Pluse_line)
    Set_Wheel_dis                            0x08002a29   Thumb Code    26  bsp_motor_iic.o(i.Set_Wheel_dis)
    Set_motor_deadzone                       0x08002a49   Thumb Code    26  bsp_motor_iic.o(i.Set_motor_deadzone)
    Set_motor_type                           0x08002a69   Thumb Code    16  bsp_motor_iic.o(i.Set_motor_type)
    SysTick_CLKSourceConfig                  0x08002a79   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08002aa1   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08002aa5   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    Target_Track                             0x08002b05   Thumb Code    96  app_motor.o(i.Target_Track)
    USART1_IRQHandler                        0x08002b6d   Thumb Code    32  bsp_usart.o(i.USART1_IRQHandler)
    USART1_Send_U8                           0x08002b91   Thumb Code    28  bsp_usart.o(i.USART1_Send_U8)
    USART1_init                              0x08002bb1   Thumb Code   174  bsp_usart.o(i.USART1_init)
    USART2_IRQHandler                        0x08002c69   Thumb Code    46  bsp_usart.o(i.USART2_IRQHandler)
    USART2_init                              0x08002ca1   Thumb Code   152  bsp_usart.o(i.USART2_init)
    USART_ClearFlag                          0x08002d41   Thumb Code    18  stm32f10x_usart.o(i.USART_ClearFlag)
    USART_ClearITPendingBit                  0x08002d53   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08002d71   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08002d89   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08002da3   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08002df7   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08002e41   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08002f19   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08002f23   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08002f2b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    Visual_Line_Track                        0x08002f31   Thumb Code   118  app_motor.o(i.Visual_Line_Track)
    __0printf$bare                           0x08002fb5   Thumb Code     8  printfb.o(i.__0printf$bare)
    __1printf$bare                           0x08002fb5   Thumb Code     0  printfb.o(i.__0printf$bare)
    __2printf                                0x08002fb5   Thumb Code     0  printfb.o(i.__0printf$bare)
    __aeabi_errno_addr                       0x08002fc5   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08002fc5   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __scatterload_copy                       0x08002fcd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002fdb   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002fdd   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    control_pwm                              0x0800300d   Thumb Code    54  bsp_motor_iic.o(i.control_pwm)
    control_speed                            0x08003049   Thumb Code    54  bsp_motor_iic.o(i.control_speed)
    delay_init                               0x08003085   Thumb Code    60  delay.o(i.delay_init)
    delay_ms                                 0x080030d1   Thumb Code    56  delay.o(i.delay_ms)
    delay_us                                 0x0800310d   Thumb Code    56  delay.o(i.delay_us)
    float_to_bytes                           0x08003149   Thumb Code     4  bsp_motor_iic.o(i.float_to_bytes)
    fputc                                    0x0800314d   Thumb Code    32  bsp_usart.o(i.fputc)
    i2cWrite                                 0x08003171   Thumb Code    96  ioi2c.o(i.i2cWrite)
    main                                     0x080031d1   Thumb Code   116  main.o(i.main)
    func_table                               0x08003318   Data         352  yb_protocol.o(.constdata)
    __ctype_table                            0x08003478   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x08003500   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003520   Number         0  anon$$obj.o(Region$$Table)
    color_mode                               0x20000000   Data           1  main.o(.data)
    Car_Auto_Drive                           0x20000004   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f10x.o(.data)
    RxIndex                                  0x20000054   Data           1  yb_protocol.o(.data)
    RxFlag                                   0x20000055   Data           1  yb_protocol.o(.data)
    New_CMD_flag                             0x20000056   Data           1  yb_protocol.o(.data)
    New_CMD_length                           0x20000057   Data           1  yb_protocol.o(.data)
    lost_count                               0x20000058   Data           4  yb_protocol.o(.data)
    Lost_Flag                                0x2000005c   Data           4  yb_protocol.o(.data)
    x                                        0x20000060   Data           4  yb_protocol.o(.data)
    y                                        0x20000064   Data           4  yb_protocol.o(.data)
    w                                        0x20000068   Data           4  yb_protocol.o(.data)
    h                                        0x2000006c   Data           4  yb_protocol.o(.data)
    id                                       0x20000070   Data           4  yb_protocol.o(.data)
    degrees                                  0x20000074   Data           4  yb_protocol.o(.data)
    x0                                       0x20000078   Data           4  yb_protocol.o(.data)
    y0                                       0x2000007c   Data           4  yb_protocol.o(.data)
    x1                                       0x20000080   Data           4  yb_protocol.o(.data)
    y1                                       0x20000084   Data           4  yb_protocol.o(.data)
    score                                    0x20000088   Data           4  yb_protocol.o(.data)
    pid_output                               0x2000008c   Data           4  app_motor.o(.data)
    pid_output1                              0x20000090   Data           4  app_motor.o(.data)
    PWM_Car_Flag                             0x20000094   Data           4  app_motor.o(.data)
    Wait_Flag                                0x20000098   Data           4  app_motor.o(.data)
    SetTarget_Flag                           0x2000009c   Data           4  app_motor.o(.data)
    target_area                              0x200000a0   Data           4  app_motor.o(.data)
    __stdout                                 0x200000c8   Data           4  stdout.o(.data)
    led_buf                                  0x200000d0   Data          56  bsp_rgb.o(.bss)
    RGB_Byte_Buffer                          0x20000108   Data         338  bsp_rgb.o(.bss)
    RxBuffer                                 0x2000025a   Data          50  yb_protocol.o(.bss)
    __initial_sp                             0x20000690   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000035f0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003520, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          257    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         3866  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         4189    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         4192    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4194    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4196    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         4197    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         4199    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         4201    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         4190    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          258    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000024   Code   RO         3869    .text               mc_w.l(memcpya.o)
    0x0800018c   0x0800018c   0x00000024   Code   RO         3871    .text               mc_w.l(memseta.o)
    0x080001b0   0x080001b0   0x0000001c   Code   RO         3873    .text               mc_w.l(strcmp.o)
    0x080001cc   0x080001cc   0x0000001a   Code   RO         4165    .text               mc_w.l(atoi.o)
    0x080001e6   0x080001e6   0x000000b0   Code   RO         4167    .text               mf_w.l(fadd.o)
    0x08000296   0x08000296   0x00000064   Code   RO         4169    .text               mf_w.l(fmul.o)
    0x080002fa   0x080002fa   0x0000007c   Code   RO         4171    .text               mf_w.l(fdiv.o)
    0x08000376   0x08000376   0x0000014e   Code   RO         4173    .text               mf_w.l(dadd.o)
    0x080004c4   0x080004c4   0x000000e4   Code   RO         4175    .text               mf_w.l(dmul.o)
    0x080005a8   0x080005a8   0x000000de   Code   RO         4177    .text               mf_w.l(ddiv.o)
    0x08000686   0x08000686   0x00000012   Code   RO         4179    .text               mf_w.l(fflti.o)
    0x08000698   0x08000698   0x00000022   Code   RO         4181    .text               mf_w.l(dflti.o)
    0x080006ba   0x080006ba   0x00000032   Code   RO         4183    .text               mf_w.l(ffixi.o)
    0x080006ec   0x080006ec   0x00000026   Code   RO         4185    .text               mf_w.l(f2d.o)
    0x08000712   0x08000712   0x00000038   Code   RO         4187    .text               mf_w.l(d2f.o)
    0x0800074a   0x0800074a   0x0000001e   Code   RO         4208    .text               mc_w.l(llshl.o)
    0x08000768   0x08000768   0x00000024   Code   RO         4210    .text               mc_w.l(llsshr.o)
    0x0800078c   0x0800078c   0x00000070   Code   RO         4221    .text               mc_w.l(strtol.o)
    0x080007fc   0x080007fc   0x00000000   Code   RO         4223    .text               mc_w.l(iusefp.o)
    0x080007fc   0x080007fc   0x0000006e   Code   RO         4224    .text               mf_w.l(fepilogue.o)
    0x0800086a   0x0800086a   0x000000ba   Code   RO         4226    .text               mf_w.l(depilogue.o)
    0x08000924   0x08000924   0x00000024   Code   RO         4232    .text               mc_w.l(init.o)
    0x08000948   0x08000948   0x00000020   Code   RO         4234    .text               mc_w.l(llushr.o)
    0x08000968   0x08000968   0x00000008   Code   RO         4236    .text               mc_w.l(ctype_o.o)
    0x08000970   0x08000970   0x0000009e   Code   RO         4264    .text               mc_w.l(_strtoul.o)
    0x08000a0e   0x08000a0e   0x0000001c   Code   RO         4266    .text               mc_w.l(_chval.o)
    0x08000a2a   0x08000a2a   0x00000002   PAD
    0x08000a2c   0x08000a2c   0x000000f4   Code   RO         3747    i.AprilTag_Track    app_motor.o
    0x08000b20   0x08000b20   0x00000034   Code   RO         3748    i.Autonomous_Avoid  app_motor.o
    0x08000b54   0x08000b54   0x00000040   Code   RO         3229    i.BSP_init          bsp.o
    0x08000b94   0x08000b94   0x00000004   Code   RO          147    i.BusFault_Handler  stm32f10x_it.o
    0x08000b98   0x08000b98   0x00000084   Code   RO         3749    i.Color_Rec         app_motor.o
    0x08000c1c   0x08000c1c   0x00000114   Code   RO         3750    i.Color_Trace       app_motor.o
    0x08000d30   0x08000d30   0x0000001c   Code   RO          987    i.DMA_ClearFlag     stm32f10x_dma.o
    0x08000d4c   0x08000d4c   0x00000018   Code   RO          989    i.DMA_Cmd           stm32f10x_dma.o
    0x08000d64   0x08000d64   0x0000014c   Code   RO          990    i.DMA_DeInit        stm32f10x_dma.o
    0x08000eb0   0x08000eb0   0x0000002c   Code   RO          992    i.DMA_GetFlagStatus  stm32f10x_dma.o
    0x08000edc   0x08000edc   0x0000003c   Code   RO          995    i.DMA_Init          stm32f10x_dma.o
    0x08000f18   0x08000f18   0x00000004   Code   RO          996    i.DMA_SetCurrDataCounter  stm32f10x_dma.o
    0x08000f1c   0x08000f1c   0x00000002   Code   RO          148    i.DebugMon_Handler  stm32f10x_it.o
    0x08000f1e   0x08000f1e   0x00000116   Code   RO         1413    i.GPIO_Init         stm32f10x_gpio.o
    0x08001034   0x08001034   0x00000090   Code   RO         1415    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x080010c4   0x080010c4   0x000000a0   Code   RO         3751    i.GazeDire_Track    app_motor.o
    0x08001164   0x08001164   0x00000034   Code   RO         3554    i.Get_LOST_Flag     yb_protocol.o
    0x08001198   0x08001198   0x00000118   Code   RO         3752    i.Hand_Track        app_motor.o
    0x080012b0   0x080012b0   0x00000028   Code   RO         3555    i.HandleAprilTag    yb_protocol.o
    0x080012d8   0x080012d8   0x00000010   Code   RO         3556    i.HandleBarcode     yb_protocol.o
    0x080012e8   0x080012e8   0x00000040   Code   RO         3557    i.HandleColor       yb_protocol.o
    0x08001328   0x08001328   0x00000012   Code   RO         3558    i.HandleDMCode      yb_protocol.o
    0x0800133a   0x0800133a   0x0000001e   Code   RO         3559    i.HandleEyeGaze     yb_protocol.o
    0x08001358   0x08001358   0x00000020   Code   RO         3560    i.HandleFaceDetect  yb_protocol.o
    0x08001378   0x08001378   0x00000040   Code   RO         3561    i.HandleFaceRecognition  yb_protocol.o
    0x080013b8   0x080013b8   0x00000040   Code   RO         3562    i.HandleFallDown    yb_protocol.o
    0x080013f8   0x080013f8   0x00000010   Code   RO         3563    i.HandleGarbageDetect  yb_protocol.o
    0x08001408   0x08001408   0x00000024   Code   RO         3564    i.HandleGuideDetect  yb_protocol.o
    0x0800142c   0x0800142c   0x00000020   Code   RO         3565    i.HandleHandDetect  yb_protocol.o
    0x0800144c   0x0800144c   0x00000006   Code   RO         3566    i.HandleHandGesture  yb_protocol.o
    0x08001452   0x08001452   0x00000034   Code   RO         3567    i.HandleLicenceDetect  yb_protocol.o
    0x08001486   0x08001486   0x00000006   Code   RO         3568    i.HandleLicenceRec  yb_protocol.o
    0x0800148c   0x0800148c   0x00000024   Code   RO         3569    i.HandleMultiColorRec  yb_protocol.o
    0x080014b0   0x080014b0   0x0000001c   Code   RO         3570    i.HandleNanoTracker  yb_protocol.o
    0x080014cc   0x080014cc   0x00000010   Code   RO         3571    i.HandleOCRRec      yb_protocol.o
    0x080014dc   0x080014dc   0x00000010   Code   RO         3572    i.HandleObjectDetect  yb_protocol.o
    0x080014ec   0x080014ec   0x00000024   Code   RO         3573    i.HandleObstacleDetect  yb_protocol.o
    0x08001510   0x08001510   0x00000020   Code   RO         3574    i.HandlePersonDetect  yb_protocol.o
    0x08001530   0x08001530   0x00000024   Code   RO         3575    i.HandleQRCode      yb_protocol.o
    0x08001554   0x08001554   0x0000002c   Code   RO         3576    i.HandleSelfLearning  yb_protocol.o
    0x08001580   0x08001580   0x00000004   Code   RO          149    i.HardFault_Handler  stm32f10x_it.o
    0x08001584   0x08001584   0x00000114   Code   RO         3753    i.HumanBody_Track   app_motor.o
    0x08001698   0x08001698   0x000000f4   Code   RO         3754    i.Human_Face_Track  app_motor.o
    0x0800178c   0x0800178c   0x0000002c   Code   RO         3340    i.IIC_Motor_Init    bsp_motor_iic.o
    0x080017b8   0x080017b8   0x00000064   Code   RO         3431    i.IIC_Send_Byte     ioi2c.o
    0x0800181c   0x0800181c   0x00000068   Code   RO         3432    i.IIC_Start         ioi2c.o
    0x08001884   0x08001884   0x00000048   Code   RO         3433    i.IIC_Stop          ioi2c.o
    0x080018cc   0x080018cc   0x00000068   Code   RO         3434    i.IIC_Wait_Ack      ioi2c.o
    0x08001934   0x08001934   0x00000130   Code   RO         3755    i.Licence_Track     app_motor.o
    0x08001a64   0x08001a64   0x00000004   Code   RO          150    i.MemManage_Handler  stm32f10x_it.o
    0x08001a68   0x08001a68   0x0000019c   Code   RO         3756    i.Motion_Car_Control  app_motor.o
    0x08001c04   0x08001c04   0x00000008   Code   RO         3757    i.Motion_Get_APB    app_motor.o
    0x08001c0c   0x08001c0c   0x00000002   Code   RO          151    i.NMI_Handler       stm32f10x_it.o
    0x08001c0e   0x08001c0e   0x00000002   PAD
    0x08001c10   0x08001c10   0x00000070   Code   RO          262    i.NVIC_Init         misc.o
    0x08001c80   0x08001c80   0x00000014   Code   RO          263    i.NVIC_PriorityGroupConfig  misc.o
    0x08001c94   0x08001c94   0x000001e8   Code   RO         3758    i.OCRrec_Actions    app_motor.o
    0x08001e7c   0x08001e7c   0x00000094   Code   RO         3759    i.PID_Calc          app_motor.o
    0x08001f10   0x08001f10   0x000000c0   Code   RO         3577    i.ParseCommonFields  yb_protocol.o
    0x08001fd0   0x08001fd0   0x00000002   Code   RO          152    i.PendSV_Handler    stm32f10x_it.o
    0x08001fd2   0x08001fd2   0x0000000c   Code   RO         3578    i.Pto_Char_To_Int   yb_protocol.o
    0x08001fde   0x08001fde   0x00000002   PAD
    0x08001fe0   0x08001fe0   0x0000002c   Code   RO         3579    i.Pto_Clear_CMD_Flag  yb_protocol.o
    0x0800200c   0x0800200c   0x000000a2   Code   RO         3580    i.Pto_Data_Parse    yb_protocol.o
    0x080020ae   0x080020ae   0x00000002   PAD
    0x080020b0   0x080020b0   0x00000090   Code   RO         3581    i.Pto_Data_Receive  yb_protocol.o
    0x08002140   0x08002140   0x00000060   Code   RO         3582    i.Pto_Loop          yb_protocol.o
    0x080021a0   0x080021a0   0x000001ec   Code   RO         3760    i.QRCode_Action     app_motor.o
    0x0800238c   0x0800238c   0x00000020   Code   RO         1828    i.RCC_AHBPeriphClockCmd  stm32f10x_rcc.o
    0x080023ac   0x080023ac   0x00000020   Code   RO         1829    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080023cc   0x080023cc   0x00000020   Code   RO         1831    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080023ec   0x080023ec   0x000000d4   Code   RO         1839    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080024c0   0x080024c0   0x00000054   Code   RO         3496    i.RGB_DMA_Init      bsp_rgb.o
    0x08002514   0x08002514   0x0000000c   Code   RO         3497    i.RGB_Driver_Init   bsp_rgb.o
    0x08002520   0x08002520   0x0000002c   Code   RO         3498    i.RGB_GPIO_Init     bsp_rgb.o
    0x0800254c   0x0800254c   0x0000000c   Code   RO         3499    i.RGB_Init          bsp_rgb.o
    0x08002558   0x08002558   0x00000038   Code   RO         3500    i.RGB_Set_Color     bsp_rgb.o
    0x08002590   0x08002590   0x00000068   Code   RO         3502    i.RGB_Spi_Init      bsp_rgb.o
    0x080025f8   0x080025f8   0x00000080   Code   RO         3503    i.RGB_Update        bsp_rgb.o
    0x08002678   0x08002678   0x000000cc   Code   RO         3761    i.RoadSign_Rec      app_motor.o
    0x08002744   0x08002744   0x00000018   Code   RO         2308    i.SPI_Cmd           stm32f10x_spi.o
    0x0800275c   0x0800275c   0x00000012   Code   RO         2314    i.SPI_I2S_DMACmd    stm32f10x_spi.o
    0x0800276e   0x0800276e   0x0000003c   Code   RO         2321    i.SPI_Init          stm32f10x_spi.o
    0x080027aa   0x080027aa   0x00000002   Code   RO          153    i.SVC_Handler       stm32f10x_it.o
    0x080027ac   0x080027ac   0x00000008   Code   RO          210    i.SetSysClock       system_stm32f10x.o
    0x080027b4   0x080027b4   0x000000e0   Code   RO          211    i.SetSysClockTo72   system_stm32f10x.o
    0x08002894   0x08002894   0x00000154   Code   RO         3762    i.Set_Motor         app_motor.o
    0x080029e8   0x080029e8   0x00000020   Code   RO         3343    i.Set_Pluse_Phase   bsp_motor_iic.o
    0x08002a08   0x08002a08   0x00000020   Code   RO         3344    i.Set_Pluse_line    bsp_motor_iic.o
    0x08002a28   0x08002a28   0x00000020   Code   RO         3345    i.Set_Wheel_dis     bsp_motor_iic.o
    0x08002a48   0x08002a48   0x00000020   Code   RO         3346    i.Set_motor_deadzone  bsp_motor_iic.o
    0x08002a68   0x08002a68   0x00000010   Code   RO         3347    i.Set_motor_type    bsp_motor_iic.o
    0x08002a78   0x08002a78   0x00000028   Code   RO          266    i.SysTick_CLKSourceConfig  misc.o
    0x08002aa0   0x08002aa0   0x00000002   Code   RO          154    i.SysTick_Handler   stm32f10x_it.o
    0x08002aa2   0x08002aa2   0x00000002   PAD
    0x08002aa4   0x08002aa4   0x00000060   Code   RO          213    i.SystemInit        system_stm32f10x.o
    0x08002b04   0x08002b04   0x00000068   Code   RO         3763    i.Target_Track      app_motor.o
    0x08002b6c   0x08002b6c   0x00000024   Code   RO         3274    i.USART1_IRQHandler  bsp_usart.o
    0x08002b90   0x08002b90   0x00000020   Code   RO         3276    i.USART1_Send_U8    bsp_usart.o
    0x08002bb0   0x08002bb0   0x000000b8   Code   RO         3277    i.USART1_init       bsp_usart.o
    0x08002c68   0x08002c68   0x00000038   Code   RO         3278    i.USART2_IRQHandler  bsp_usart.o
    0x08002ca0   0x08002ca0   0x000000a0   Code   RO         3281    i.USART2_init       bsp_usart.o
    0x08002d40   0x08002d40   0x00000012   Code   RO         2995    i.USART_ClearFlag   stm32f10x_usart.o
    0x08002d52   0x08002d52   0x0000001e   Code   RO         2996    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08002d70   0x08002d70   0x00000018   Code   RO         2999    i.USART_Cmd         stm32f10x_usart.o
    0x08002d88   0x08002d88   0x0000001a   Code   RO         3002    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08002da2   0x08002da2   0x00000054   Code   RO         3003    i.USART_GetITStatus  stm32f10x_usart.o
    0x08002df6   0x08002df6   0x0000004a   Code   RO         3005    i.USART_ITConfig    stm32f10x_usart.o
    0x08002e40   0x08002e40   0x000000d8   Code   RO         3006    i.USART_Init        stm32f10x_usart.o
    0x08002f18   0x08002f18   0x0000000a   Code   RO         3013    i.USART_ReceiveData  stm32f10x_usart.o
    0x08002f22   0x08002f22   0x00000008   Code   RO         3016    i.USART_SendData    stm32f10x_usart.o
    0x08002f2a   0x08002f2a   0x00000004   Code   RO          155    i.UsageFault_Handler  stm32f10x_it.o
    0x08002f2e   0x08002f2e   0x00000002   PAD
    0x08002f30   0x08002f30   0x00000084   Code   RO         3764    i.Visual_Line_Track  app_motor.o
    0x08002fb4   0x08002fb4   0x00000010   Code   RO         3878    i.__0printf$bare    mc_w.l(printfb.o)
    0x08002fc4   0x08002fc4   0x00000008   Code   RO         4214    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x08002fcc   0x08002fcc   0x0000000e   Code   RO         4270    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002fda   0x08002fda   0x00000002   Code   RO         4271    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002fdc   0x08002fdc   0x0000000e   Code   RO         4272    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002fea   0x08002fea   0x00000022   Code   RO         3885    i._printf_core      mc_w.l(printfb.o)
    0x0800300c   0x0800300c   0x0000003c   Code   RO         3349    i.control_pwm       bsp_motor_iic.o
    0x08003048   0x08003048   0x0000003c   Code   RO         3350    i.control_speed     bsp_motor_iic.o
    0x08003084   0x08003084   0x0000004c   Code   RO         3248    i.delay_init        delay.o
    0x080030d0   0x080030d0   0x0000003c   Code   RO         3249    i.delay_ms          delay.o
    0x0800310c   0x0800310c   0x0000003c   Code   RO         3250    i.delay_us          delay.o
    0x08003148   0x08003148   0x00000004   Code   RO         3351    i.float_to_bytes    bsp_motor_iic.o
    0x0800314c   0x0800314c   0x00000024   Code   RO         3283    i.fputc             bsp_usart.o
    0x08003170   0x08003170   0x00000060   Code   RO         3436    i.i2cWrite          ioi2c.o
    0x080031d0   0x080031d0   0x00000088   Code   RO            1    i.main              main.o
    0x08003258   0x08003258   0x00000220   Data   RO         3584    .constdata          yb_protocol.o
    0x08003478   0x08003478   0x00000081   Data   RO         4237    .constdata          mc_w.l(ctype_o.o)
    0x080034f9   0x080034f9   0x00000003   PAD
    0x080034fc   0x080034fc   0x00000004   Data   RO         4238    .constdata          mc_w.l(ctype_o.o)
    0x08003500   0x08003500   0x00000020   Data   RO         4268    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003520, Size: 0x00000690, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003520   0x00000008   Data   RW            2    .data               main.o
    0x20000008   0x08003528   0x00000014   Data   RW          214    .data               system_stm32f10x.o
    0x2000001c   0x0800353c   0x00000014   Data   RW         1859    .data               stm32f10x_rcc.o
    0x20000030   0x08003550   0x00000004   Data   RW         3251    .data               delay.o
    0x20000034   0x08003554   0x00000020   Data   RW         3353    .data               bsp_motor_iic.o
    0x20000054   0x08003574   0x00000038   Data   RW         3585    .data               yb_protocol.o
    0x2000008c   0x080035ac   0x0000003c   Data   RW         3765    .data               app_motor.o
    0x200000c8   0x080035e8   0x00000004   Data   RW         4203    .data               mc_w.l(stdout.o)
    0x200000cc   0x080035ec   0x00000004   Data   RW         4217    .data               mc_w.l(errno.o)
    0x200000d0        -       0x0000018a   Zero   RW         3504    .bss                bsp_rgb.o
    0x2000025a        -       0x00000032   Zero   RW         3583    .bss                yb_protocol.o
    0x2000028c   0x080035f0   0x00000004   PAD
    0x20000290        -       0x00000400   Zero   RW          255    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      4296        594          0         60          0      14735   app_motor.o
        64          4          0          0          0        527   bsp.o
       312         40          0         32          0       7284   bsp_motor_iic.o
       440         44          0          0        394       4922   bsp_rgb.o
       504         40          0          0          0       3957   bsp_usart.o
         0          0          0          0          0         32   core_cm3.o
       196         24          0          4          0       1955   delay.o
       476         48          0          0          0       3725   ioi2c.o
       136         20          0          8          0     251736   main.o
       172         22          0          0          0       2253   misc.o
        36          8        304          0       1024        848   startup_stm32f10x_hd.o
       492         26          0          0          0       5451   stm32f10x_dma.o
       422          6          0          0          0       2938   stm32f10x_gpio.o
        26          0          0          0          0       4342   stm32f10x_it.o
       308         38          0         20          0       5322   stm32f10x_rcc.o
       102          0          0          0          0       2914   stm32f10x_spi.o
       490          6          0          0          0       7861   stm32f10x_usart.o
       328         28          0         20          0       2529   system_stm32f10x.o
      1422         92        544         56         50      24845   yb_protocol.o

    ----------------------------------------------------------------------
     10232       <USER>        <GROUP>        200       1472     348176   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        10          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
         8          4          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
        50          8          0          0          0        152   printfb.o
         0          0          0          4          0          0   stdout.o
        28          0          0          0          0         76   strcmp.o
       112          0          0          0          0         88   strtol.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      2352         <USER>        <GROUP>          8          0       2500   Library Totals
         2          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       674         32        133          8          0       1140   mc_w.l
      1676          0          0          0          0       1360   mf_w.l

    ----------------------------------------------------------------------
      2352         <USER>        <GROUP>          8          0       2500   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12584       1072       1016        208       1472     343600   Grand Totals
     12584       1072       1016        208       1472     343600   ELF Image Totals
     12584       1072       1016        208          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13600 (  13.28kB)
    Total RW  Size (RW Data + ZI Data)              1680 (   1.64kB)
    Total ROM Size (Code + RO Data + RW Data)      13808 (  13.48kB)

==============================================================================

