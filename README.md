# YB协议HAL库 - 4567工程通信模块

## 概述

本项目是基于workplace工程通信协议，使用STM32 HAL库重新实现的通信模块。该模块支持STM32与K230之间的视觉识别数据通信，包括颜色识别、人脸检测、二维码识别等多种AI视觉功能。

## 特性

### 核心功能
- ✅ 基于HAL库的串口通信
- ✅ 模块化协议解析引擎
- ✅ 多协议ID支持(22种视觉识别功能)
- ✅ 回调函数机制
- ✅ 错误处理和超时检测
- ✅ 统计信息收集

### 支持的协议类型
1. **颜色识别** (ID=1) - 返回位置和尺寸信息
2. **条形码识别** (ID=2) - 返回位置、尺寸和内容
3. **二维码识别** (ID=3) - 返回位置、尺寸和内容
4. **AprilTag识别** (ID=4) - 返回位置、尺寸、ID和角度
5. **人脸检测** (ID=6) - 返回位置和尺寸信息
6. **人体检测** (ID=9) - 返回位置和尺寸信息
7. **手掌检测** (ID=11) - 返回位置和尺寸信息
8. **物体检测** (ID=14) - 返回位置、尺寸和类别
9. **OCR识别** (ID=13) - 返回识别文本
10. **手势识别** (ID=12) - 返回手势名称

## 文件结构

```
├── yb_protocol_hal.h      # 底层协议头文件
├── yb_protocol_hal.c      # 底层协议实现
├── yb_protocol_app.h      # 应用层接口头文件
├── yb_protocol_app.c      # 应用层接口实现
├── yb_protocol_config.h   # 配置文件
├── yb_protocol_example.c  # 使用示例
└── README.md             # 说明文档
```

## 快速开始

### 1. 硬件连接

```
STM32 UART2 ←→ K230 UART
PA2 (TX)    ←→ RX
PA3 (RX)    ←→ TX
GND         ←→ GND
```

### 2. 基本使用

#### 颜色追踪示例

```c
#include "yb_protocol_app.h"

// 全局变量
static yb_uart_comm_t uart_comm;
static yb_protocol_app_t protocol_app;
static UART_HandleTypeDef huart2;

// 颜色检测回调函数
void on_color_detected(const yb_color_data_t* data) {
    printf("Color at: x=%d, y=%d, size=%dx%d\n", 
           data->x, data->y, data->w, data->h);
    
    // 控制机器人运动
    if (data->x < 320) {
        // 左转
    } else if (data->x > 320) {
        // 右转
    } else {
        // 前进
    }
}

int main(void) {
    // HAL库初始化
    HAL_Init();
    SystemClock_Config();
    
    // 初始化UART
    huart2.Instance = USART2;
    huart2.Init.BaudRate = 115200;
    // ... 其他UART配置
    HAL_UART_Init(&huart2);
    
    // 快速初始化颜色追踪
    yb_app_quick_init_color_tracking(&protocol_app, &uart_comm, on_color_detected);
    
    // 主循环
    while (1) {
        yb_app_process(&protocol_app);
        HAL_Delay(10);
    }
}
```

#### 多功能检测示例

```c
// 注册多个回调函数
yb_app_init(&protocol_app, &uart_comm);
yb_app_register_qrcode_callback(&protocol_app, on_qrcode_detected);
yb_app_register_face_detect_callback(&protocol_app, on_face_detected);
yb_app_register_object_detect_callback(&protocol_app, on_object_detected);

// 主循环处理
while (1) {
    yb_app_process(&protocol_app);
    
    // 检查连接状态
    if (!yb_app_is_connected(&protocol_app)) {
        printf("Connection lost\n");
    }
    
    HAL_Delay(10);
}
```

### 3. HAL回调函数配置

在`stm32f4xx_it.c`或相应的中断处理文件中添加：

```c
extern yb_uart_comm_t g_uart_comm;

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART2) {
        uint8_t rx_byte = g_uart_comm.protocol.rx_buffer[g_uart_comm.protocol.rx_index - 1];
        yb_protocol_rx_byte(&g_uart_comm, rx_byte);
        yb_protocol_uart_rx_callback(&g_uart_comm);
    }
}

void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART2) {
        yb_protocol_uart_error_callback(&g_uart_comm);
    }
}
```

## API参考

### 底层协议API

#### 初始化函数
```c
HAL_StatusTypeDef yb_protocol_init(yb_uart_comm_t* comm, UART_HandleTypeDef* huart);
HAL_StatusTypeDef yb_protocol_deinit(yb_uart_comm_t* comm);
```

#### 处理器注册
```c
HAL_StatusTypeDef yb_protocol_register_handler(yb_uart_comm_t* comm, 
                                               yb_protocol_id_t id,
                                               const yb_field_meta_t* fields,
                                               uint8_t field_count,
                                               yb_protocol_handler_t handler);
```

#### 数据处理
```c
void yb_protocol_rx_byte(yb_uart_comm_t* comm, uint8_t byte);
HAL_StatusTypeDef yb_protocol_process(yb_uart_comm_t* comm);
```

### 应用层API

#### 快速初始化
```c
HAL_StatusTypeDef yb_app_quick_init_color_tracking(yb_protocol_app_t* app, 
                                                   yb_uart_comm_t* comm,
                                                   yb_color_callback_t callback);
```

#### 回调注册
```c
HAL_StatusTypeDef yb_app_register_qrcode_callback(yb_protocol_app_t* app, 
                                                  yb_qrcode_callback_t callback);
```

#### 状态查询
```c
bool yb_app_is_connected(const yb_protocol_app_t* app);
void yb_app_get_statistics(const yb_protocol_app_t* app, 
                           uint32_t* rx_count,
                           uint32_t* success_count,
                           uint32_t* error_count);
```

## 协议格式

### 数据包格式
```
$长度,功能ID,数据字段1,数据字段2,...#
```

### 示例数据包
```
$8,1,160,120,50,30#     // 颜色识别: x=160, y=120, w=50, h=30
$15,3,100,80,40,25,HELLO#  // 二维码: x=100, y=80, w=40, h=25, 内容="HELLO"
```

## 配置选项

在`yb_protocol_config.h`中可以配置：

- 缓冲区大小: `YB_PTO_BUF_LEN_MAX`
- 最大字段数: `YB_PTO_MAX_FIELDS`
- 超时时间: `YB_PTO_DEFAULT_TIMEOUT_MS`
- 调试开关: `YB_PROTOCOL_DEBUG`
- 功能开关: `YB_ENABLE_*_DETECTION`

## 移植指南

### 不同STM32系列移植

1. 修改`yb_protocol_config.h`中的GPIO和UART配置
2. 根据芯片型号调整时钟配置
3. 确认HAL库版本兼容性

### 自定义协议扩展

1. 在`yb_protocol_id_t`枚举中添加新ID
2. 定义字段元数据数组
3. 实现处理函数
4. 注册到应用层

## 故障排除

### 常见问题

1. **无法接收数据**
   - 检查UART配置和引脚连接
   - 确认波特率匹配(115200)
   - 检查中断是否正确配置

2. **数据解析错误**
   - 检查协议格式是否正确
   - 确认字段数量和类型匹配
   - 查看调试输出信息

3. **连接丢失**
   - 检查超时配置
   - 确认K230端正常发送数据
   - 查看统计信息中的错误计数

### 调试方法

1. 开启调试输出: `#define YB_PROTOCOL_DEBUG 1`
2. 查看统计信息: `yb_app_get_statistics()`
3. 监控连接状态: `yb_app_is_connected()`

## 版本历史

- **v1.0.0** - 初始版本，支持基本协议功能
- 基于workplace工程协议移植
- 支持22种视觉识别协议
- 提供HAL库兼容接口

## 许可证

本项目基于workplace工程开发，用于4567工程内部使用。

## 贡献

欢迎提交问题和改进建议。请确保代码符合项目编码规范。
