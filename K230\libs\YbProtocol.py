"""
YbProtocol - K230协议处理模块
基于workplace工程的协议实现，为4567工程提供协议封装和解析功能

作者: 4567工程团队
日期: 2024
版本: 1.0.0
"""

import time


class YbProtocol:
    """
    YB协议处理类
    提供协议数据的封装、解析和格式化功能
    """
    
    # 协议常量定义
    PTO_HEAD = 0x24  # '$'
    PTO_TAIL = 0x23  # '#'
    PTO_FIELD_SEPARATOR = ','
    PTO_BUF_LEN_MAX = 128
    
    # 协议ID定义 (与workplace工程保持一致)
    ID_COLOR = 1           # 颜色识别
    ID_BARCODE = 2         # 条形码识别
    ID_QRCODE = 3          # 二维码识别
    ID_APRILTAG = 4        # AprilTag识别
    ID_DMCODE = 5          # DataMatrix码
    ID_FACE_DETECT = 6     # 人脸检测
    ID_EYE_GAZE = 7        # 注视方向
    ID_FACE_RECOGNITION = 8 # 人脸识别
    ID_PERSON_DETECT = 9   # 人体检测
    ID_FALLDOWN_DETECT = 10 # 跌倒检测
    ID_HAND_DETECT = 11    # 手掌检测
    ID_HAND_GESTURE = 12   # 手势识别
    ID_OCR_REC = 13        # OCR字符识别
    ID_OBJECT_DETECT = 14  # 物体检测
    ID_NANO_TRACKER = 15   # 目标跟踪
    ID_SELF_LEARNING = 16  # 自学习物体识别
    ID_LICENCE_REC = 17    # 车牌识别
    ID_LICENCE_DETECT = 18 # 车牌检测
    ID_GARBAGE_DETECT = 19 # 垃圾检测
    ID_GUIDE_DETECT = 20   # 路标检测
    ID_OBSTACLE_DETECT = 21 # 障碍检测
    ID_MULTI_COLOR = 22    # 多颜色识别
    
    def __init__(self):
        """初始化协议处理器"""
        self.debug_enabled = True
        self.packet_count = 0
        self.error_count = 0
        
        print("[YbProtocol] Protocol processor initialized")
    
    def _debug_print(self, message):
        """调试输出"""
        if self.debug_enabled:
            print(f"[YbProtocol] {message}")
    
    def _format_data_fields(self, *args):
        """
        格式化数据字段
        
        Args:
            *args: 数据字段
            
        Returns:
            str: 格式化后的字段字符串
        """
        fields = []
        for arg in args:
            if isinstance(arg, (int, float)):
                fields.append(str(int(arg)))  # 转换为整数字符串
            elif isinstance(arg, str):
                fields.append(arg)
            else:
                fields.append(str(arg))
        
        return self.PTO_FIELD_SEPARATOR.join(fields)
    
    def _calculate_length(self, protocol_id, data_fields):
        """
        计算协议长度
        
        Args:
            protocol_id: 协议ID
            data_fields: 数据字段字符串
            
        Returns:
            int: 协议长度
        """
        # 长度 = 协议ID长度 + 1个逗号 + 数据字段长度
        return len(str(protocol_id)) + 1 + len(data_fields)
    
    def create_protocol_packet(self, protocol_id, *args):
        """
        创建协议数据包
        
        Args:
            protocol_id: 协议ID
            *args: 数据字段
            
        Returns:
            str: 完整的协议数据包
        """
        try:
            # 格式化数据字段
            data_fields = self._format_data_fields(*args)
            
            # 计算长度
            length = self._calculate_length(protocol_id, data_fields)
            
            # 构建完整数据包: $长度,协议ID,数据字段#
            packet = f"{chr(self.PTO_HEAD)}{length},{protocol_id},{data_fields}{chr(self.PTO_TAIL)}"
            
            self.packet_count += 1
            self._debug_print(f"Created packet: {packet}")
            
            return packet
            
        except Exception as e:
            self.error_count += 1
            self._debug_print(f"Create packet error: {e}")
            return None
    
    # 各种协议数据创建方法
    def get_color_data(self, x, y, w, h):
        """创建颜色识别数据包"""
        return self.create_protocol_packet(self.ID_COLOR, x, y, w, h)
    
    def get_barcode_data(self, x, y, w, h, message):
        """创建条形码识别数据包"""
        return self.create_protocol_packet(self.ID_BARCODE, x, y, w, h, message)
    
    def get_qrcode_data(self, x, y, w, h, message):
        """创建二维码识别数据包"""
        return self.create_protocol_packet(self.ID_QRCODE, x, y, w, h, message)
    
    def get_apriltag_data(self, x, y, w, h, tag_id, degrees):
        """创建AprilTag识别数据包"""
        return self.create_protocol_packet(self.ID_APRILTAG, x, y, w, h, tag_id, degrees)
    
    def get_dmcode_data(self, x, y, w, h, message, degrees):
        """创建DataMatrix码数据包"""
        return self.create_protocol_packet(self.ID_DMCODE, x, y, w, h, message, degrees)
    
    def get_face_detect_data(self, x, y, w, h):
        """创建人脸检测数据包"""
        return self.create_protocol_packet(self.ID_FACE_DETECT, x, y, w, h)
    
    def get_eye_gaze_data(self, x0, y0, x1, y1):
        """创建注视方向数据包"""
        return self.create_protocol_packet(self.ID_EYE_GAZE, x0, y0, x1, y1)
    
    def get_face_recognition_data(self, x, y, w, h, name, score):
        """创建人脸识别数据包"""
        return self.create_protocol_packet(self.ID_FACE_RECOGNITION, x, y, w, h, name, score)
    
    def get_person_detect_data(self, x, y, w, h):
        """创建人体检测数据包"""
        return self.create_protocol_packet(self.ID_PERSON_DETECT, x, y, w, h)
    
    def get_falldown_detect_data(self, x, y, w, h, state, score):
        """创建跌倒检测数据包"""
        return self.create_protocol_packet(self.ID_FALLDOWN_DETECT, x, y, w, h, state, score)
    
    def get_hand_detect_data(self, x, y, w, h):
        """创建手掌检测数据包"""
        return self.create_protocol_packet(self.ID_HAND_DETECT, x, y, w, h)
    
    def get_hand_gesture_data(self, gesture):
        """创建手势识别数据包"""
        return self.create_protocol_packet(self.ID_HAND_GESTURE, gesture)
    
    def get_ocr_data(self, text):
        """创建OCR识别数据包"""
        return self.create_protocol_packet(self.ID_OCR_REC, text)
    
    def get_object_detect_data(self, x, y, w, h, class_name):
        """创建物体检测数据包"""
        return self.create_protocol_packet(self.ID_OBJECT_DETECT, x, y, w, h, class_name)
    
    def get_nano_tracker_data(self, x, y, w, h):
        """创建目标跟踪数据包"""
        return self.create_protocol_packet(self.ID_NANO_TRACKER, x, y, w, h)
    
    def get_self_learning_data(self, category, score):
        """创建自学习识别数据包"""
        return self.create_protocol_packet(self.ID_SELF_LEARNING, category, score)
    
    def get_licence_rec_data(self, licence_text):
        """创建车牌识别数据包"""
        return self.create_protocol_packet(self.ID_LICENCE_REC, licence_text)
    
    def get_licence_detect_data(self, x, y, w, h, x1, y1, x2, y2):
        """创建车牌检测数据包"""
        return self.create_protocol_packet(self.ID_LICENCE_DETECT, x, y, w, h, x1, y1, x2, y2)
    
    def get_garbage_detect_data(self, x, y, w, h, label):
        """创建垃圾检测数据包"""
        return self.create_protocol_packet(self.ID_GARBAGE_DETECT, x, y, w, h, label)
    
    def get_guide_detect_data(self, x, y, w, h, label):
        """创建路标检测数据包"""
        return self.create_protocol_packet(self.ID_GUIDE_DETECT, x, y, w, h, label)
    
    def get_obstacle_detect_data(self, x, y, w, h, label):
        """创建障碍检测数据包"""
        return self.create_protocol_packet(self.ID_OBSTACLE_DETECT, x, y, w, h, label)
    
    def get_multi_color_data(self, x, y, w, h, color):
        """创建多颜色识别数据包"""
        return self.create_protocol_packet(self.ID_MULTI_COLOR, x, y, w, h, color)
    
    def parse_protocol_packet(self, packet):
        """
        解析协议数据包
        
        Args:
            packet: 协议数据包字符串
            
        Returns:
            dict: 解析结果，包含协议ID和数据字段
        """
        try:
            # 检查包头包尾
            if not packet.startswith(chr(self.PTO_HEAD)) or not packet.endswith(chr(self.PTO_TAIL)):
                self._debug_print(f"Invalid packet format: {packet}")
                return None
            
            # 去除包头包尾
            content = packet[1:-1]
            
            # 分割字段
            fields = content.split(self.PTO_FIELD_SEPARATOR)
            
            if len(fields) < 2:
                self._debug_print(f"Insufficient fields: {fields}")
                return None
            
            # 解析长度和协议ID
            length = int(fields[0])
            protocol_id = int(fields[1])
            data_fields = fields[2:] if len(fields) > 2 else []
            
            result = {
                'length': length,
                'protocol_id': protocol_id,
                'data_fields': data_fields,
                'raw_packet': packet
            }
            
            self._debug_print(f"Parsed packet: ID={protocol_id}, Fields={data_fields}")
            return result
            
        except Exception as e:
            self.error_count += 1
            self._debug_print(f"Parse packet error: {e}")
            return None
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'packet_count': self.packet_count,
            'error_count': self.error_count,
            'debug_enabled': self.debug_enabled
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.packet_count = 0
        self.error_count = 0
        self._debug_print("Statistics reset")
    
    def set_debug(self, enabled):
        """设置调试模式"""
        self.debug_enabled = enabled
        self._debug_print(f"Debug mode: {'enabled' if enabled else 'disabled'}")


# 便捷函数
def create_color_packet(x, y, w, h):
    """创建颜色识别数据包的便捷函数"""
    protocol = YbProtocol()
    return protocol.get_color_data(x, y, w, h)


def create_qrcode_packet(x, y, w, h, message):
    """创建二维码识别数据包的便捷函数"""
    protocol = YbProtocol()
    return protocol.get_qrcode_data(x, y, w, h, message)


def test_protocol():
    """协议测试函数"""
    print("[YbProtocol] Starting protocol test")
    
    protocol = YbProtocol()
    
    # 测试各种数据包创建
    test_cases = [
        ("Color", protocol.get_color_data(160, 120, 50, 30)),
        ("QRCode", protocol.get_qrcode_data(100, 80, 40, 25, "HELLO")),
        ("Face", protocol.get_face_detect_data(200, 150, 80, 100)),
        ("Object", protocol.get_object_detect_data(300, 200, 60, 80, "person")),
        ("OCR", protocol.get_ocr_data("FORWARD")),
        ("Gesture", protocol.get_hand_gesture_data("thumbs_up"))
    ]
    
    for name, packet in test_cases:
        if packet:
            print(f"[YbProtocol] {name}: {packet}")
            
            # 测试解析
            parsed = protocol.parse_protocol_packet(packet)
            if parsed:
                print(f"[YbProtocol] Parsed {name}: ID={parsed['protocol_id']}, Fields={parsed['data_fields']}")
            else:
                print(f"[YbProtocol] Failed to parse {name}")
        else:
            print(f"[YbProtocol] Failed to create {name} packet")
    
    # 打印统计信息
    stats = protocol.get_statistics()
    print(f"[YbProtocol] Test completed: {stats['packet_count']} packets, {stats['error_count']} errors")


if __name__ == "__main__":
    # 测试代码
    test_protocol()
