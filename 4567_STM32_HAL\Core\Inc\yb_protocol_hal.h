#ifndef _YB_PROTOCOL_HAL_H_
#define _YB_PROTOCOL_HAL_H_

#include "stm32f4xx_hal.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* 协议配置参数 Protocol Configuration Parameters */
#define YB_PTO_BUF_LEN_MAX           (128)    // 协议缓冲区最大长度
#define YB_PTO_HEAD                  (0x24)  // 协议头 '$'
#define YB_PTO_TAIL                  (0x23)  // 协议尾 '#'
#define YB_PTO_FIELD_SEPARATOR       (',')   // 字段分隔符
#define YB_PTO_MAX_FIELDS            (10)    // 最大字段数量
#define YB_PTO_MAX_HANDLERS          (32)    // 最大处理器数量

/* 协议功能ID枚举 Protocol Function ID Enumeration */
typedef enum {
    YB_ID_COLOR = 1,           // 颜色识别
    YB_ID_BARCODE = 2,         // 条形码识别
    YB_ID_QRCODE = 3,          // 二维码识别
    YB_ID_APRILTAG = 4,        // AprilTag识别
    YB_ID_DMCODE = 5,          // DataMatrix码
    YB_ID_FACE_DETECT = 6,     // 人脸检测
    YB_ID_EYE_GAZE = 7,        // 注视方向
    YB_ID_FACE_RECOGNITION = 8, // 人脸识别
    YB_ID_PERSON_DETECT = 9,   // 人体检测
    YB_ID_FALLDOWN_DETECT = 10, // 跌倒检测
    YB_ID_HAND_DETECT = 11,    // 手掌检测
    YB_ID_HAND_GESTURE = 12,   // 手势识别
    YB_ID_OCR_REC = 13,        // OCR字符识别
    YB_ID_OBJECT_DETECT = 14,  // 物体检测
    YB_ID_NANO_TRACKER = 15,   // 目标跟踪
    YB_ID_SELF_LEARNING = 16,  // 自学习物体识别
    YB_ID_LICENCE_REC = 17,    // 车牌识别
    YB_ID_LICENCE_DETECT = 18, // 车牌检测
    YB_ID_GARBAGE_DETECT = 19, // 垃圾检测
    YB_ID_GUIDE_DETECT = 20,   // 路标检测
    YB_ID_OBSTACLE_DETECT = 21, // 障碍检测
    YB_ID_MULTI_COLOR = 22,    // 多颜色识别
} yb_protocol_id_t;

/* 字段类型枚举 Field Type Enumeration */
typedef enum {
    YB_FIELD_INT,      // 整数字段
    YB_FIELD_STRING    // 字符串字段
} yb_field_type_t;

/* 字段元数据结构 Field Metadata Structure */
typedef struct {
    uint8_t pos;              // 字段在协议中的位置
    yb_field_type_t type;     // 字段类型
} yb_field_meta_t;

/* 协议数据结构 Protocol Data Structure */
typedef struct {
    int values[YB_PTO_MAX_FIELDS];                    // 整数值数组
    char strings[YB_PTO_MAX_FIELDS][YB_PTO_BUF_LEN_MAX]; // 字符串数组
    uint8_t field_count;                              // 字段数量
    yb_protocol_id_t protocol_id;                     // 协议ID
} yb_protocol_data_t;

/* 协议处理函数类型定义 Protocol Handler Function Type */
typedef void (*yb_protocol_handler_t)(const yb_protocol_data_t* data);

/* 功能描述结构 Function Description Structure */
typedef struct {
    yb_protocol_id_t id;                    // 协议ID
    const yb_field_meta_t* fields;         // 字段元数据数组
    uint8_t field_count;                    // 字段数量
    yb_protocol_handler_t handler;          // 处理函数
} yb_func_desc_t;

/* 协议状态枚举 Protocol State Enumeration */
typedef enum {
    YB_PTO_STATE_IDLE,        // 空闲状态
    YB_PTO_STATE_RECEIVING,   // 接收状态
    YB_PTO_STATE_READY        // 数据就绪状态
} yb_protocol_state_t;

/* 协议控制结构 Protocol Control Structure */
typedef struct {
    uint8_t rx_buffer[YB_PTO_BUF_LEN_MAX];    // 接收缓冲区
    uint16_t rx_index;                        // 接收索引
    uint16_t rx_length;                       // 接收长度
    yb_protocol_state_t state;                // 协议状态
    bool new_data_flag;                       // 新数据标志
    
    // 处理器注册表
    yb_func_desc_t handlers[YB_PTO_MAX_HANDLERS];
    uint8_t handler_count;                    // 已注册处理器数量
    
    // 统计信息
    uint32_t rx_count;                        // 接收计数
    uint32_t parse_success_count;             // 解析成功计数
    uint32_t parse_error_count;               // 解析错误计数
    
    // 超时控制
    uint32_t last_rx_time;                    // 最后接收时间
    uint32_t timeout_ms;                      // 超时时间(毫秒)
    bool lost_flag;                           // 丢失标志
} yb_protocol_ctrl_t;

/* 串口通信结构 UART Communication Structure */
typedef struct {
    UART_HandleTypeDef* huart;                // HAL库UART句柄
    yb_protocol_ctrl_t protocol;              // 协议控制结构
    bool initialized;                         // 初始化标志
} yb_uart_comm_t;

/* 函数声明 Function Declarations */

/* 初始化和配置函数 Initialization and Configuration Functions */
HAL_StatusTypeDef yb_protocol_init(yb_uart_comm_t* comm, UART_HandleTypeDef* huart);
HAL_StatusTypeDef yb_protocol_deinit(yb_uart_comm_t* comm);
HAL_StatusTypeDef yb_protocol_set_timeout(yb_uart_comm_t* comm, uint32_t timeout_ms);

/* 处理器注册函数 Handler Registration Functions */
HAL_StatusTypeDef yb_protocol_register_handler(yb_uart_comm_t* comm, 
                                               yb_protocol_id_t id,
                                               const yb_field_meta_t* fields,
                                               uint8_t field_count,
                                               yb_protocol_handler_t handler);
HAL_StatusTypeDef yb_protocol_unregister_handler(yb_uart_comm_t* comm, yb_protocol_id_t id);

/* 数据处理函数 Data Processing Functions */
void yb_protocol_rx_byte(yb_uart_comm_t* comm, uint8_t byte);
HAL_StatusTypeDef yb_protocol_process(yb_uart_comm_t* comm);
void yb_protocol_clear_buffer(yb_uart_comm_t* comm);

/* 数据发送函数 Data Transmission Functions */
HAL_StatusTypeDef yb_protocol_send_data(yb_uart_comm_t* comm, const uint8_t* data, uint16_t length);
HAL_StatusTypeDef yb_protocol_send_string(yb_uart_comm_t* comm, const char* str);

/* 状态查询函数 Status Query Functions */
bool yb_protocol_is_data_ready(const yb_uart_comm_t* comm);
bool yb_protocol_is_lost(const yb_uart_comm_t* comm);
void yb_protocol_get_statistics(const yb_uart_comm_t* comm, 
                                uint32_t* rx_count,
                                uint32_t* success_count,
                                uint32_t* error_count);

/* 工具函数 Utility Functions */
int yb_protocol_char_to_int(const char* str);
uint32_t yb_protocol_get_tick(void);

/* 中断回调函数 Interrupt Callback Functions */
void yb_protocol_uart_rx_callback(yb_uart_comm_t* comm);
void yb_protocol_uart_error_callback(yb_uart_comm_t* comm);

/* 预定义字段元数据 Predefined Field Metadata */
extern const yb_field_meta_t yb_color_fields[];
extern const yb_field_meta_t yb_barcode_fields[];
extern const yb_field_meta_t yb_qrcode_fields[];
extern const yb_field_meta_t yb_apriltag_fields[];
extern const yb_field_meta_t yb_face_detect_fields[];
extern const yb_field_meta_t yb_person_detect_fields[];
extern const yb_field_meta_t yb_hand_detect_fields[];
extern const yb_field_meta_t yb_object_detect_fields[];

#endif /* _YB_PROTOCOL_HAL_H_ */
