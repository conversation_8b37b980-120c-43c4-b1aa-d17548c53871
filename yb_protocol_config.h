#ifndef _YB_PROTOCOL_CONFIG_H_
#define _YB_PROTOCOL_CONFIG_H_

/**
 * @file yb_protocol_config.h
 * @brief YB协议HAL库配置文件
 * <AUTHOR>
 * @date 2024
 */

/* 协议配置 Protocol Configuration */

// 缓冲区大小配置
#define YB_PTO_BUF_LEN_MAX           128     // 协议缓冲区最大长度
#define YB_PTO_MAX_FIELDS            10      // 最大字段数量
#define YB_PTO_MAX_HANDLERS          32      // 最大处理器数量

// 协议字符定义
#define YB_PTO_HEAD                  0x24    // 协议头 '$'
#define YB_PTO_TAIL                  0x23    // 协议尾 '#'
#define YB_PTO_FIELD_SEPARATOR       ','     // 字段分隔符

// 超时配置
#define YB_PTO_DEFAULT_TIMEOUT_MS    1000    // 默认超时时间(毫秒)
#define YB_PTO_LOST_THRESHOLD        50      // 丢失判断阈值

/* UART配置 UART Configuration */

// UART参数配置
#define YB_UART_BAUDRATE             115200  // 波特率
#define YB_UART_WORD_LENGTH          UART_WORDLENGTH_8B
#define YB_UART_STOP_BITS            UART_STOPBITS_1
#define YB_UART_PARITY               UART_PARITY_NONE
#define YB_UART_HW_FLOW_CTRL         UART_HWCONTROL_NONE
#define YB_UART_OVERSAMPLING         UART_OVERSAMPLING_16

// UART实例选择 (根据实际硬件修改)
#define YB_UART_INSTANCE             USART2
#define YB_UART_IRQn                 USART2_IRQn
#define YB_UART_IRQ_PRIORITY         5

/* GPIO配置 GPIO Configuration */

// UART GPIO引脚配置 (STM32F4为例，根据实际硬件修改)
#define YB_UART_TX_PORT              GPIOA
#define YB_UART_TX_PIN               GPIO_PIN_2
#define YB_UART_RX_PORT              GPIOA
#define YB_UART_RX_PIN               GPIO_PIN_3
#define YB_UART_GPIO_AF              GPIO_AF7_USART2

// GPIO时钟使能
#define YB_UART_GPIO_CLK_ENABLE()    __HAL_RCC_GPIOA_CLK_ENABLE()
#define YB_UART_CLK_ENABLE()         __HAL_RCC_USART2_CLK_ENABLE()
#define YB_UART_CLK_DISABLE()        __HAL_RCC_USART2_CLK_DISABLE()

/* 调试配置 Debug Configuration */

// 调试开关
#define YB_PROTOCOL_DEBUG            1       // 1:开启调试, 0:关闭调试

// 调试输出配置
#if YB_PROTOCOL_DEBUG
    #include <stdio.h>
    #define YB_DEBUG_PRINT(fmt, ...)    printf("[YB_PROTOCOL] " fmt "\r\n", ##__VA_ARGS__)
    #define YB_APP_DEBUG_PRINT(fmt, ...)    printf("[YB_APP] " fmt "\r\n", ##__VA_ARGS__)
#else
    #define YB_DEBUG_PRINT(fmt, ...)
    #define YB_APP_DEBUG_PRINT(fmt, ...)
#endif

/* 功能配置 Feature Configuration */

// 协议功能开关
#define YB_ENABLE_COLOR_DETECTION    1       // 颜色识别
#define YB_ENABLE_BARCODE_DETECTION  1       // 条形码识别
#define YB_ENABLE_QRCODE_DETECTION   1       // 二维码识别
#define YB_ENABLE_APRILTAG_DETECTION 1       // AprilTag识别
#define YB_ENABLE_FACE_DETECTION     1       // 人脸检测
#define YB_ENABLE_PERSON_DETECTION   1       // 人体检测
#define YB_ENABLE_HAND_DETECTION     1       // 手掌检测
#define YB_ENABLE_OBJECT_DETECTION   1       // 物体检测
#define YB_ENABLE_OCR_RECOGNITION    1       // OCR识别
#define YB_ENABLE_GESTURE_RECOGNITION 1      // 手势识别

// 应用层功能开关
#define YB_ENABLE_QUICK_INIT         1       // 快速初始化功能
#define YB_ENABLE_STATISTICS         1       // 统计功能
#define YB_ENABLE_AUTO_RECONNECT     1       // 自动重连功能

/* 性能配置 Performance Configuration */

// 内存优化
#define YB_USE_STATIC_MEMORY         1       // 使用静态内存分配
#define YB_OPTIMIZE_FOR_SIZE         0       // 优化代码大小
#define YB_OPTIMIZE_FOR_SPEED        1       // 优化执行速度

// 中断优先级配置
#define YB_UART_IRQ_PREEMPT_PRIORITY 1       // 抢占优先级
#define YB_UART_IRQ_SUB_PRIORITY     0       // 子优先级

/* 错误处理配置 Error Handling Configuration */

// 错误处理策略
#define YB_ERROR_RECOVERY_ENABLED    1       // 启用错误恢复
#define YB_MAX_ERROR_COUNT           10      // 最大错误计数
#define YB_ERROR_RESET_THRESHOLD     100     // 错误重置阈值

// 断言配置
#ifdef DEBUG
    #define YB_ASSERT(expr)          assert(expr)
#else
    #define YB_ASSERT(expr)          ((void)0)
#endif

/* 兼容性配置 Compatibility Configuration */

// STM32系列兼容性
#if defined(STM32F4xx)
    #define YB_STM32F4_SERIES        1
#elif defined(STM32F1xx)
    #define YB_STM32F1_SERIES        1
#elif defined(STM32H7xx)
    #define YB_STM32H7_SERIES        1
#else
    #warning "Unsupported STM32 series, please check compatibility"
#endif

// HAL库版本兼容性
#if defined(HAL_UART_MODULE_ENABLED)
    #define YB_HAL_UART_AVAILABLE    1
#else
    #error "HAL UART module is required"
#endif

/* 应用示例配置 Application Example Configuration */

// 示例选择
#define COLOR_TRACKING_EXAMPLE       0       // 颜色追踪示例
#define MULTI_DETECTION_EXAMPLE      1       // 多功能检测示例
#define CUSTOM_APPLICATION_EXAMPLE   0       // 自定义应用示例

/* 版本信息 Version Information */

#define YB_PROTOCOL_VERSION_MAJOR    1
#define YB_PROTOCOL_VERSION_MINOR    0
#define YB_PROTOCOL_VERSION_PATCH    0
#define YB_PROTOCOL_VERSION_STRING   "1.0.0"

/* 编译时检查 Compile Time Checks */

// 缓冲区大小检查
#if YB_PTO_BUF_LEN_MAX < 32
    #error "Buffer size too small, minimum 32 bytes required"
#endif

// 字段数量检查
#if YB_PTO_MAX_FIELDS < 5
    #error "Maximum fields too small, minimum 5 fields required"
#endif

// 处理器数量检查
#if YB_PTO_MAX_HANDLERS < 10
    #error "Maximum handlers too small, minimum 10 handlers required"
#endif

#endif /* _YB_PROTOCOL_CONFIG_H_ */
