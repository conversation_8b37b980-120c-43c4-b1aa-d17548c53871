"""
YbComm - K230综合通信管理模块
整合YbUart和YbProtocol，为4567工程提供完整的通信解决方案

作者: 4567工程团队
日期: 2024
版本: 1.0.0
"""

import time
import _thread
from ybUtils.YbUart import YbUart
from libs.YbProtocol import YbProtocol


class YbComm:
    """
    YB综合通信管理类
    整合串口通信和协议处理功能
    """
    
    def __init__(self, uart_id=1, baudrate=115200, auto_start=True):
        """
        初始化通信管理器
        
        Args:
            uart_id: UART端口号
            baudrate: 波特率
            auto_start: 是否自动启动
        """
        self.uart_id = uart_id
        self.baudrate = baudrate
        
        # 初始化串口和协议
        self.uart = YbUart(uart_id=uart_id, baudrate=baudrate)
        self.protocol = YbProtocol()
        
        # 状态控制
        self.running = False
        self.thread_id = None
        self.initialized = False
        
        # 统计信息
        self.send_count = 0
        self.send_success_count = 0
        self.send_error_count = 0
        
        # 检查初始化状态
        if self.uart.is_initialized():
            self.initialized = True
            print(f"[YbComm] Communication manager initialized on UART{uart_id}")
            
            if auto_start:
                self.start()
        else:
            print(f"[YbComm] Initialization failed")
    
    def is_initialized(self):
        """检查是否初始化成功"""
        return self.initialized and self.uart.is_initialized()
    
    def start(self):
        """启动通信管理器"""
        if not self.is_initialized():
            print("[YbComm] Cannot start: not initialized")
            return False
        
        if not self.running:
            self.running = True
            self.thread_id = _thread.start_new_thread(self._comm_thread, ())
            print("[YbComm] Communication manager started")
            return True
        
        return False
    
    def stop(self):
        """停止通信管理器"""
        if self.running:
            self.running = False
            time.sleep(0.1)  # 等待线程结束
            print("[YbComm] Communication manager stopped")
    
    def _comm_thread(self):
        """通信处理线程"""
        print("[YbComm] Communication thread started")
        
        while self.running:
            try:
                # 这里可以添加接收数据处理逻辑
                # 目前主要用于发送，接收处理可以根据需要扩展
                time.sleep(0.01)  # 10ms延时
            except Exception as e:
                print(f"[YbComm] Thread error: {e}")
                time.sleep(0.1)
        
        print("[YbComm] Communication thread stopped")
    
    def _send_packet(self, packet):
        """
        发送数据包
        
        Args:
            packet: 协议数据包
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_initialized():
            return False
        
        self.send_count += 1
        
        if packet and self.uart.send(packet):
            self.send_success_count += 1
            return True
        else:
            self.send_error_count += 1
            return False
    
    # 各种数据发送方法
    def send_color_data(self, x, y, w, h):
        """发送颜色识别数据"""
        packet = self.protocol.get_color_data(x, y, w, h)
        return self._send_packet(packet)
    
    def send_barcode_data(self, x, y, w, h, message):
        """发送条形码识别数据"""
        packet = self.protocol.get_barcode_data(x, y, w, h, message)
        return self._send_packet(packet)
    
    def send_qrcode_data(self, x, y, w, h, message):
        """发送二维码识别数据"""
        packet = self.protocol.get_qrcode_data(x, y, w, h, message)
        return self._send_packet(packet)
    
    def send_apriltag_data(self, x, y, w, h, tag_id, degrees):
        """发送AprilTag识别数据"""
        packet = self.protocol.get_apriltag_data(x, y, w, h, tag_id, degrees)
        return self._send_packet(packet)
    
    def send_face_detect_data(self, x, y, w, h):
        """发送人脸检测数据"""
        packet = self.protocol.get_face_detect_data(x, y, w, h)
        return self._send_packet(packet)
    
    def send_eye_gaze_data(self, x0, y0, x1, y1):
        """发送注视方向数据"""
        packet = self.protocol.get_eye_gaze_data(x0, y0, x1, y1)
        return self._send_packet(packet)
    
    def send_face_recognition_data(self, x, y, w, h, name, score):
        """发送人脸识别数据"""
        packet = self.protocol.get_face_recognition_data(x, y, w, h, name, score)
        return self._send_packet(packet)
    
    def send_person_detect_data(self, x, y, w, h):
        """发送人体检测数据"""
        packet = self.protocol.get_person_detect_data(x, y, w, h)
        return self._send_packet(packet)
    
    def send_falldown_detect_data(self, x, y, w, h, state, score):
        """发送跌倒检测数据"""
        packet = self.protocol.get_falldown_detect_data(x, y, w, h, state, score)
        return self._send_packet(packet)
    
    def send_hand_detect_data(self, x, y, w, h):
        """发送手掌检测数据"""
        packet = self.protocol.get_hand_detect_data(x, y, w, h)
        return self._send_packet(packet)
    
    def send_hand_gesture_data(self, gesture):
        """发送手势识别数据"""
        packet = self.protocol.get_hand_gesture_data(gesture)
        return self._send_packet(packet)
    
    def send_ocr_data(self, text):
        """发送OCR识别数据"""
        packet = self.protocol.get_ocr_data(text)
        return self._send_packet(packet)
    
    def send_object_detect_data(self, x, y, w, h, class_name):
        """发送物体检测数据"""
        packet = self.protocol.get_object_detect_data(x, y, w, h, class_name)
        return self._send_packet(packet)
    
    def send_nano_tracker_data(self, x, y, w, h):
        """发送目标跟踪数据"""
        packet = self.protocol.get_nano_tracker_data(x, y, w, h)
        return self._send_packet(packet)
    
    def send_licence_rec_data(self, licence_text):
        """发送车牌识别数据"""
        packet = self.protocol.get_licence_rec_data(licence_text)
        return self._send_packet(packet)
    
    def send_licence_detect_data(self, x, y, w, h, x1, y1, x2, y2):
        """发送车牌检测数据"""
        packet = self.protocol.get_licence_detect_data(x, y, w, h, x1, y1, x2, y2)
        return self._send_packet(packet)
    
    def send_garbage_detect_data(self, x, y, w, h, label):
        """发送垃圾检测数据"""
        packet = self.protocol.get_garbage_detect_data(x, y, w, h, label)
        return self._send_packet(packet)
    
    def send_guide_detect_data(self, x, y, w, h, label):
        """发送路标检测数据"""
        packet = self.protocol.get_guide_detect_data(x, y, w, h, label)
        return self._send_packet(packet)
    
    def send_obstacle_detect_data(self, x, y, w, h, label):
        """发送障碍检测数据"""
        packet = self.protocol.get_obstacle_detect_data(x, y, w, h, label)
        return self._send_packet(packet)
    
    def send_multi_color_data(self, x, y, w, h, color):
        """发送多颜色识别数据"""
        packet = self.protocol.get_multi_color_data(x, y, w, h, color)
        return self._send_packet(packet)
    
    def send_custom_data(self, protocol_id, *args):
        """发送自定义协议数据"""
        packet = self.protocol.create_protocol_packet(protocol_id, *args)
        return self._send_packet(packet)
    
    def get_statistics(self):
        """获取综合统计信息"""
        uart_stats = self.uart.get_statistics()
        protocol_stats = self.protocol.get_statistics()
        
        return {
            'comm': {
                'initialized': self.initialized,
                'running': self.running,
                'send_count': self.send_count,
                'send_success_count': self.send_success_count,
                'send_error_count': self.send_error_count,
                'success_rate': self.send_success_count / max(self.send_count, 1) * 100
            },
            'uart': uart_stats,
            'protocol': protocol_stats
        }
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        
        print(f"[YbComm] Communication Statistics:")
        print(f"  Initialized: {stats['comm']['initialized']}")
        print(f"  Running: {stats['comm']['running']}")
        print(f"  Send Count: {stats['comm']['send_count']}")
        print(f"  Send Success: {stats['comm']['send_success_count']}")
        print(f"  Send Error: {stats['comm']['send_error_count']}")
        print(f"  Success Rate: {stats['comm']['success_rate']:.1f}%")
        
        print(f"[YbComm] UART Statistics:")
        print(f"  TX Count: {stats['uart']['tx_count']}")
        print(f"  TX Bytes: {stats['uart']['tx_bytes']}")
        print(f"  Error Count: {stats['uart']['error_count']}")
        
        print(f"[YbComm] Protocol Statistics:")
        print(f"  Packet Count: {stats['protocol']['packet_count']}")
        print(f"  Error Count: {stats['protocol']['error_count']}")
    
    def reset_statistics(self):
        """重置所有统计信息"""
        self.send_count = 0
        self.send_success_count = 0
        self.send_error_count = 0
        self.uart.reset_statistics()
        self.protocol.reset_statistics()
        print("[YbComm] All statistics reset")
    
    def set_debug(self, enabled):
        """设置调试模式"""
        self.protocol.set_debug(enabled)
        print(f"[YbComm] Debug mode: {'enabled' if enabled else 'disabled'}")
    
    def deinit(self):
        """反初始化通信管理器"""
        self.stop()
        if self.uart:
            self.uart.deinit()
        self.initialized = False
        print("[YbComm] Communication manager deinitialized")
    
    def __del__(self):
        """析构函数"""
        self.deinit()


# 便捷函数
def create_comm(uart_id=1, baudrate=115200):
    """
    创建通信管理器的便捷函数
    
    Args:
        uart_id: UART端口号
        baudrate: 波特率
        
    Returns:
        YbComm: 通信管理器实例
    """
    return YbComm(uart_id=uart_id, baudrate=baudrate)


def test_comm():
    """通信管理器测试函数"""
    print("[YbComm] Starting communication test")
    
    # 创建通信管理器
    comm = YbComm(uart_id=1, baudrate=115200)
    
    if not comm.is_initialized():
        print("[YbComm] Test failed: initialization error")
        return
    
    # 测试各种数据发送
    test_cases = [
        ("Color", lambda: comm.send_color_data(160, 120, 50, 30)),
        ("QRCode", lambda: comm.send_qrcode_data(100, 80, 40, 25, "HELLO")),
        ("Face", lambda: comm.send_face_detect_data(200, 150, 80, 100)),
        ("Object", lambda: comm.send_object_detect_data(300, 200, 60, 80, "person")),
        ("OCR", lambda: comm.send_ocr_data("FORWARD")),
        ("Gesture", lambda: comm.send_hand_gesture_data("thumbs_up"))
    ]
    
    for name, send_func in test_cases:
        if send_func():
            print(f"[YbComm] {name} data sent successfully")
        else:
            print(f"[YbComm] {name} data send failed")
        
        time.sleep(0.1)  # 间隔100ms
    
    # 打印统计信息
    comm.print_statistics()
    
    # 清理
    comm.deinit()
    print("[YbComm] Test completed")


if __name__ == "__main__":
    # 测试代码
    test_comm()
