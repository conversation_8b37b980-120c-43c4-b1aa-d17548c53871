"""
颜色追踪示例 - K230端
演示如何使用YbComm进行颜色识别数据通信

作者: 4567工程团队
日期: 2024
"""

import time
import sensor
import image
import lcd
from libs.YbComm import YbComm


class ColorTracker:
    """颜色追踪器"""
    
    def __init__(self, uart_id=1, baudrate=115200):
        """
        初始化颜色追踪器
        
        Args:
            uart_id: UART端口号
            baudrate: 波特率
        """
        # 初始化通信
        self.comm = YbComm(uart_id=uart_id, baudrate=baudrate)
        
        # 颜色阈值 (LAB色彩空间)
        self.red_threshold = (30, 100, 15, 127, 15, 127)      # 红色
        self.green_threshold = (30, 100, -64, -8, -32, 32)    # 绿色
        self.blue_threshold = (0, 30, 0, 64, -128, 0)         # 蓝色
        
        # 当前追踪的颜色
        self.current_color = "red"
        self.color_thresholds = {
            "red": self.red_threshold,
            "green": self.green_threshold,
            "blue": self.blue_threshold
        }
        
        # 统计信息
        self.frame_count = 0
        self.detection_count = 0
        self.send_count = 0
        
        print("[ColorTracker] Color tracker initialized")
    
    def init_camera(self):
        """初始化摄像头"""
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)  # 320x240
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            
            # 初始化LCD显示
            lcd.init()
            lcd.clear()
            
            print("[ColorTracker] Camera initialized")
            return True
        except Exception as e:
            print(f"[ColorTracker] Camera init error: {e}")
            return False
    
    def set_color(self, color):
        """
        设置要追踪的颜色
        
        Args:
            color: 颜色名称 ("red", "green", "blue")
        """
        if color in self.color_thresholds:
            self.current_color = color
            print(f"[ColorTracker] Tracking color set to: {color}")
        else:
            print(f"[ColorTracker] Unknown color: {color}")
    
    def detect_color(self, img):
        """
        检测颜色
        
        Args:
            img: 图像对象
            
        Returns:
            list: 检测到的色块列表
        """
        threshold = self.color_thresholds[self.current_color]
        
        # 查找色块
        blobs = img.find_blobs([threshold], 
                              pixels_threshold=200, 
                              area_threshold=200, 
                              merge=True)
        
        return blobs
    
    def process_frame(self):
        """处理一帧图像"""
        try:
            # 获取图像
            img = sensor.snapshot()
            self.frame_count += 1
            
            # 检测颜色
            blobs = self.detect_color(img)
            
            if blobs:
                # 找到最大的色块
                largest_blob = max(blobs, key=lambda b: b.pixels())
                
                # 获取位置信息
                x = largest_blob.cx()
                y = largest_blob.cy()
                w = largest_blob.w()
                h = largest_blob.h()
                
                # 绘制检测框
                img.draw_rectangle(largest_blob.rect(), color=(255, 0, 0))
                img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0))
                
                # 显示信息
                info_text = f"{self.current_color}: ({x},{y}) {w}x{h}"
                img.draw_string(10, 10, info_text, color=(255, 255, 255))
                
                # 发送数据
                if self.comm.send_color_data(x, y, w, h):
                    self.send_count += 1
                    print(f"[ColorTracker] Color data sent: x={x}, y={y}, w={w}, h={h}")
                
                self.detection_count += 1
            else:
                # 未检测到目标
                img.draw_string(10, 10, f"No {self.current_color} detected", color=(255, 255, 255))
            
            # 显示统计信息
            stats_text = f"F:{self.frame_count} D:{self.detection_count} S:{self.send_count}"
            img.draw_string(10, 220, stats_text, color=(255, 255, 0))
            
            # 显示图像
            lcd.display(img)
            
            return len(blobs) > 0
            
        except Exception as e:
            print(f"[ColorTracker] Process frame error: {e}")
            return False
    
    def run(self, duration=None):
        """
        运行颜色追踪
        
        Args:
            duration: 运行时长(秒)，None表示无限运行
        """
        if not self.comm.is_initialized():
            print("[ColorTracker] Communication not initialized")
            return
        
        if not self.init_camera():
            print("[ColorTracker] Camera initialization failed")
            return
        
        print(f"[ColorTracker] Starting color tracking for '{self.current_color}'")
        
        start_time = time.ticks_ms()
        
        try:
            while True:
                # 处理一帧
                self.process_frame()
                
                # 检查运行时长
                if duration:
                    elapsed = time.ticks_diff(time.ticks_ms(), start_time) / 1000
                    if elapsed >= duration:
                        break
                
                # 短暂延时
                time.sleep(0.05)  # 20fps
                
        except KeyboardInterrupt:
            print("[ColorTracker] Stopped by user")
        except Exception as e:
            print(f"[ColorTracker] Runtime error: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("[ColorTracker] Cleaning up...")
        
        # 打印统计信息
        self.print_statistics()
        
        # 清理通信
        if self.comm:
            self.comm.deinit()
        
        # 清理显示
        try:
            lcd.clear()
        except:
            pass
    
    def print_statistics(self):
        """打印统计信息"""
        print(f"[ColorTracker] Session Statistics:")
        print(f"  Frames processed: {self.frame_count}")
        print(f"  Detections: {self.detection_count}")
        print(f"  Data sent: {self.send_count}")
        
        if self.frame_count > 0:
            detection_rate = self.detection_count / self.frame_count * 100
            print(f"  Detection rate: {detection_rate:.1f}%")
        
        # 打印通信统计
        if self.comm:
            self.comm.print_statistics()


def main():
    """主函数"""
    print("=== K230 Color Tracking Example ===")
    
    # 创建颜色追踪器
    tracker = ColorTracker(uart_id=1, baudrate=115200)
    
    # 设置追踪颜色 (可以改为 "green" 或 "blue")
    tracker.set_color("red")
    
    # 运行追踪 (运行30秒，可以改为None无限运行)
    tracker.run(duration=30)
    
    print("=== Color Tracking Example Completed ===")


def test_communication():
    """测试通信功能"""
    print("=== Communication Test ===")
    
    comm = YbComm(uart_id=1, baudrate=115200)
    
    if not comm.is_initialized():
        print("Communication initialization failed")
        return
    
    # 发送测试数据
    test_data = [
        (160, 120, 50, 30),
        (200, 150, 60, 40),
        (100, 80, 40, 35),
        (250, 180, 70, 50)
    ]
    
    for i, (x, y, w, h) in enumerate(test_data):
        if comm.send_color_data(x, y, w, h):
            print(f"Test {i+1}: Color data sent successfully")
        else:
            print(f"Test {i+1}: Color data send failed")
        
        time.sleep(1)
    
    comm.print_statistics()
    comm.deinit()
    
    print("=== Communication Test Completed ===")


if __name__ == "__main__":
    # 选择运行模式
    mode = input("Select mode (1: Color Tracking, 2: Communication Test): ")
    
    if mode == "1":
        main()
    elif mode == "2":
        test_communication()
    else:
        print("Invalid mode selected")
        main()  # 默认运行颜色追踪
