"""
YB协议HAL库 - K230端实现
基于workplace工程协议，为4567工程提供HAL库风格的通信接口

作者: 4567工程团队
日期: 2024
版本: 1.0.0
"""

import time
from machine import UART
import _thread


class YbProtocolHAL:
    """YB协议HAL库主类"""
    
    # 协议常量定义
    PTO_HEAD = 0x24  # '$'
    PTO_TAIL = 0x23  # '#'
    PTO_FIELD_SEPARATOR = ','
    PTO_BUF_LEN_MAX = 128
    PTO_MAX_FIELDS = 10
    
    # 协议ID枚举
    ID_COLOR = 1
    ID_BARCODE = 2
    ID_QRCODE = 3
    ID_APRILTAG = 4
    ID_DMCODE = 5
    ID_FACE_DETECT = 6
    ID_EYE_GAZE = 7
    ID_FACE_RECOGNITION = 8
    ID_PERSON_DETECT = 9
    ID_FALLDOWN_DETECT = 10
    ID_HAND_DETECT = 11
    ID_HAND_GESTURE = 12
    ID_OCR_REC = 13
    ID_OBJECT_DETECT = 14
    ID_NANO_TRACKER = 15
    ID_SELF_LEARNING = 16
    ID_LICENCE_REC = 17
    ID_LICENCE_DETECT = 18
    ID_GARBAGE_DETECT = 19
    ID_GUIDE_DETECT = 20
    ID_OBSTACLE_DETECT = 21
    ID_MULTI_COLOR = 22
    
    # 协议状态枚举
    STATE_IDLE = 0
    STATE_RECEIVING = 1
    STATE_READY = 2
    
    def __init__(self, uart_id=1, baudrate=115200, timeout_ms=1000):
        """
        初始化YB协议HAL库
        
        Args:
            uart_id: UART端口号
            baudrate: 波特率
            timeout_ms: 超时时间(毫秒)
        """
        self.uart_id = uart_id
        self.baudrate = baudrate
        self.timeout_ms = timeout_ms
        
        # 初始化UART
        self.uart = UART(uart_id, baudrate, 8, None, 1, timeout=1000, read_buf_len=4096)
        
        # 协议状态
        self.state = self.STATE_IDLE
        self.rx_buffer = bytearray(self.PTO_BUF_LEN_MAX)
        self.rx_index = 0
        self.rx_length = 0
        self.new_data_flag = False
        
        # 统计信息
        self.tx_count = 0
        self.rx_count = 0
        self.parse_success_count = 0
        self.parse_error_count = 0
        
        # 超时控制
        self.last_tx_time = 0
        self.lost_flag = False
        
        # 线程控制
        self.running = False
        self.thread_id = None
        
        print(f"[YbProtocolHAL] Initialized on UART{uart_id} at {baudrate} baud")
    
    def start(self):
        """启动协议处理"""
        if not self.running:
            self.running = True
            self.thread_id = _thread.start_new_thread(self._rx_thread, ())
            print("[YbProtocolHAL] Protocol processing started")
    
    def stop(self):
        """停止协议处理"""
        self.running = False
        if self.thread_id:
            # 等待线程结束
            time.sleep(0.1)
        print("[YbProtocolHAL] Protocol processing stopped")
    
    def _rx_thread(self):
        """接收线程"""
        while self.running:
            try:
                if self.uart.any():
                    data = self.uart.read(1)
                    if data:
                        self._process_rx_byte(data[0])
                time.sleep(0.001)  # 1ms延时
            except Exception as e:
                print(f"[YbProtocolHAL] RX thread error: {e}")
                time.sleep(0.01)
    
    def _process_rx_byte(self, byte):
        """处理接收到的字节"""
        if self.state == self.STATE_IDLE:
            if byte == self.PTO_HEAD:
                self.rx_buffer[0] = self.PTO_HEAD
                self.rx_index = 1
                self.state = self.STATE_RECEIVING
                self.last_rx_time = time.ticks_ms()
        
        elif self.state == self.STATE_RECEIVING:
            if self.rx_index < self.PTO_BUF_LEN_MAX:
                self.rx_buffer[self.rx_index] = byte
                self.rx_index += 1
                self.last_rx_time = time.ticks_ms()
                
                if byte == self.PTO_TAIL:
                    self.rx_length = self.rx_index
                    self.state = self.STATE_READY
                    self.new_data_flag = True
                    self.rx_count += 1
            else:
                # 缓冲区溢出，重置状态
                self._reset_rx_state()
    
    def _reset_rx_state(self):
        """重置接收状态"""
        self.state = self.STATE_IDLE
        self.rx_index = 0
        self.rx_length = 0
        self.new_data_flag = False
        for i in range(self.PTO_BUF_LEN_MAX):
            self.rx_buffer[i] = 0
    
    def send_protocol_data(self, protocol_id, *args):
        """
        发送协议数据
        
        Args:
            protocol_id: 协议ID
            *args: 数据字段
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建数据字符串
            data_fields = [str(arg) for arg in args]
            data_str = ','.join(data_fields)
            
            # 计算长度(包含协议ID)
            length = len(data_str) + len(str(protocol_id)) + 1  # +1 for comma
            
            # 构建完整协议包
            packet = f"${length},{protocol_id},{data_str}#"
            
            # 发送数据
            self.uart.write(packet.encode())
            self.tx_count += 1
            self.last_tx_time = time.ticks_ms()
            self.lost_flag = False
            
            print(f"[YbProtocolHAL] Sent: {packet}")
            return True
            
        except Exception as e:
            print(f"[YbProtocolHAL] Send error: {e}")
            return False
    
    def send_color_data(self, x, y, w, h):
        """发送颜色识别数据"""
        return self.send_protocol_data(self.ID_COLOR, x, y, w, h)
    
    def send_qrcode_data(self, x, y, w, h, message):
        """发送二维码识别数据"""
        return self.send_protocol_data(self.ID_QRCODE, x, y, w, h, message)
    
    def send_barcode_data(self, x, y, w, h, message):
        """发送条形码识别数据"""
        return self.send_protocol_data(self.ID_BARCODE, x, y, w, h, message)
    
    def send_apriltag_data(self, x, y, w, h, tag_id, degrees):
        """发送AprilTag识别数据"""
        return self.send_protocol_data(self.ID_APRILTAG, x, y, w, h, tag_id, degrees)
    
    def send_face_detect_data(self, x, y, w, h):
        """发送人脸检测数据"""
        return self.send_protocol_data(self.ID_FACE_DETECT, x, y, w, h)
    
    def send_person_detect_data(self, x, y, w, h):
        """发送人体检测数据"""
        return self.send_protocol_data(self.ID_PERSON_DETECT, x, y, w, h)
    
    def send_hand_detect_data(self, x, y, w, h):
        """发送手掌检测数据"""
        return self.send_protocol_data(self.ID_HAND_DETECT, x, y, w, h)
    
    def send_object_detect_data(self, x, y, w, h, class_name):
        """发送物体检测数据"""
        return self.send_protocol_data(self.ID_OBJECT_DETECT, x, y, w, h, class_name)
    
    def send_ocr_data(self, text):
        """发送OCR识别数据"""
        return self.send_protocol_data(self.ID_OCR_REC, text)
    
    def send_gesture_data(self, gesture):
        """发送手势识别数据"""
        return self.send_protocol_data(self.ID_HAND_GESTURE, gesture)
    
    def send_eye_gaze_data(self, x0, y0, x1, y1):
        """发送注视方向数据"""
        return self.send_protocol_data(self.ID_EYE_GAZE, x0, y0, x1, y1)
    
    def send_nano_tracker_data(self, x, y, w, h):
        """发送目标跟踪数据"""
        return self.send_protocol_data(self.ID_NANO_TRACKER, x, y, w, h)
    
    def is_connected(self):
        """检查连接状态"""
        if self.last_tx_time == 0:
            return True  # 还未发送过数据
        
        current_time = time.ticks_ms()
        time_diff = time.ticks_diff(current_time, self.last_tx_time)
        
        # 如果超过超时时间没有发送数据，认为连接丢失
        if time_diff > self.timeout_ms:
            self.lost_flag = True
        
        return not self.lost_flag
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'tx_count': self.tx_count,
            'rx_count': self.rx_count,
            'parse_success_count': self.parse_success_count,
            'parse_error_count': self.parse_error_count,
            'lost_flag': self.lost_flag,
            'state': self.state
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.tx_count = 0
        self.rx_count = 0
        self.parse_success_count = 0
        self.parse_error_count = 0
        print("[YbProtocolHAL] Statistics reset")
    
    def set_timeout(self, timeout_ms):
        """设置超时时间"""
        self.timeout_ms = timeout_ms
        print(f"[YbProtocolHAL] Timeout set to {timeout_ms}ms")
    
    def flush_rx_buffer(self):
        """清空接收缓冲区"""
        self._reset_rx_state()
        if self.uart.any():
            self.uart.read()  # 清空UART缓冲区
        print("[YbProtocolHAL] RX buffer flushed")
    
    def __del__(self):
        """析构函数"""
        self.stop()


class YbProtocolConfig:
    """YB协议配置类"""
    
    # 默认配置
    DEFAULT_UART_ID = 1
    DEFAULT_BAUDRATE = 115200
    DEFAULT_TIMEOUT_MS = 1000
    
    # 调试配置
    DEBUG_ENABLED = True
    VERBOSE_LOGGING = False
    
    # 性能配置
    RX_THREAD_DELAY_MS = 1
    TX_RETRY_COUNT = 3
    
    @classmethod
    def get_default_config(cls):
        """获取默认配置"""
        return {
            'uart_id': cls.DEFAULT_UART_ID,
            'baudrate': cls.DEFAULT_BAUDRATE,
            'timeout_ms': cls.DEFAULT_TIMEOUT_MS,
            'debug_enabled': cls.DEBUG_ENABLED,
            'verbose_logging': cls.VERBOSE_LOGGING
        }
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        config = cls.get_default_config()
        print("[YbProtocolConfig] Current configuration:")
        for key, value in config.items():
            print(f"  {key}: {value}")


# 全局实例(可选)
_global_protocol_instance = None

def get_global_protocol():
    """获取全局协议实例"""
    global _global_protocol_instance
    if _global_protocol_instance is None:
        _global_protocol_instance = YbProtocolHAL()
    return _global_protocol_instance

def init_global_protocol(uart_id=1, baudrate=115200, timeout_ms=1000):
    """初始化全局协议实例"""
    global _global_protocol_instance
    _global_protocol_instance = YbProtocolHAL(uart_id, baudrate, timeout_ms)
    return _global_protocol_instance
