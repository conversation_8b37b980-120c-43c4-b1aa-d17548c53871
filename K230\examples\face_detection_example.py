"""
人脸检测示例 - K230端
演示如何使用YbComm进行人脸检测数据通信

作者: 4567工程团队
日期: 2024
"""

import time
import sensor
import image
import lcd
from libs.YbComm import YbComm


class FaceDetector:
    """人脸检测器"""
    
    def __init__(self, uart_id=1, baudrate=115200):
        """
        初始化人脸检测器
        
        Args:
            uart_id: UART端口号
            baudrate: 波特率
        """
        # 初始化通信
        self.comm = YbComm(uart_id=uart_id, baudrate=baudrate)
        
        # 人脸检测参数
        self.face_cascade = image.HaarCascade("frontalface", stages=25)
        self.min_face_size = (30, 30)  # 最小人脸尺寸
        
        # 统计信息
        self.frame_count = 0
        self.detection_count = 0
        self.send_count = 0
        self.last_send_time = 0
        
        print("[FaceDetector] Face detector initialized")
    
    def init_camera(self):
        """初始化摄像头"""
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.GRAYSCALE)  # 人脸检测使用灰度图像
            sensor.set_framesize(sensor.QVGA)  # 320x240
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            
            # 初始化LCD显示
            lcd.init()
            lcd.clear()
            
            print("[FaceDetector] Camera initialized")
            return True
        except Exception as e:
            print(f"[FaceDetector] Camera init error: {e}")
            return False
    
    def detect_faces(self, img):
        """
        检测人脸
        
        Args:
            img: 图像对象
            
        Returns:
            list: 检测到的人脸列表
        """
        try:
            # 使用Haar级联检测人脸
            faces = img.find_features(self.face_cascade, 
                                    threshold=0.75, 
                                    scale_factor=1.25)
            
            # 过滤太小的检测结果
            valid_faces = []
            for face in faces:
                if face[2] >= self.min_face_size[0] and face[3] >= self.min_face_size[1]:
                    valid_faces.append(face)
            
            return valid_faces
        except Exception as e:
            print(f"[FaceDetector] Face detection error: {e}")
            return []
    
    def process_frame(self):
        """处理一帧图像"""
        try:
            # 获取图像
            img = sensor.snapshot()
            self.frame_count += 1
            
            # 检测人脸
            faces = self.detect_faces(img)
            
            if faces:
                face_count = len(faces)
                
                # 处理每个检测到的人脸
                for i, face in enumerate(faces):
                    x, y, w, h = face
                    
                    # 绘制检测框
                    img.draw_rectangle((x, y, w, h), color=255)
                    
                    # 绘制人脸中心点
                    center_x = x + w // 2
                    center_y = y + h // 2
                    img.draw_cross(center_x, center_y, color=255)
                    
                    # 显示人脸编号
                    img.draw_string(x, y-10, f"Face{i+1}", color=255)
                
                # 显示检测信息
                info_text = f"Faces: {face_count}"
                img.draw_string(10, 10, info_text, color=255)
                
                # 发送最大的人脸数据 (避免频繁发送)
                current_time = time.ticks_ms()
                if time.ticks_diff(current_time, self.last_send_time) > 200:  # 200ms间隔
                    
                    # 找到最大的人脸
                    largest_face = max(faces, key=lambda f: f[2] * f[3])
                    x, y, w, h = largest_face
                    
                    # 发送数据
                    if self.comm.send_face_detect_data(x, y, w, h):
                        self.send_count += 1
                        self.last_send_time = current_time
                        print(f"[FaceDetector] Face data sent: x={x}, y={y}, w={w}, h={h}")
                
                self.detection_count += 1
            else:
                # 未检测到人脸
                img.draw_string(10, 10, "No face detected", color=255)
            
            # 显示统计信息
            stats_text = f"F:{self.frame_count} D:{self.detection_count} S:{self.send_count}"
            img.draw_string(10, 220, stats_text, color=255)
            
            # 显示图像
            lcd.display(img)
            
            return len(faces) > 0
            
        except Exception as e:
            print(f"[FaceDetector] Process frame error: {e}")
            return False
    
    def run(self, duration=None):
        """
        运行人脸检测
        
        Args:
            duration: 运行时长(秒)，None表示无限运行
        """
        if not self.comm.is_initialized():
            print("[FaceDetector] Communication not initialized")
            return
        
        if not self.init_camera():
            print("[FaceDetector] Camera initialization failed")
            return
        
        print("[FaceDetector] Starting face detection")
        
        start_time = time.ticks_ms()
        
        try:
            while True:
                # 处理一帧
                self.process_frame()
                
                # 检查运行时长
                if duration:
                    elapsed = time.ticks_diff(time.ticks_ms(), start_time) / 1000
                    if elapsed >= duration:
                        break
                
                # 短暂延时
                time.sleep(0.05)  # 20fps
                
        except KeyboardInterrupt:
            print("[FaceDetector] Stopped by user")
        except Exception as e:
            print(f"[FaceDetector] Runtime error: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("[FaceDetector] Cleaning up...")
        
        # 打印统计信息
        self.print_statistics()
        
        # 清理通信
        if self.comm:
            self.comm.deinit()
        
        # 清理显示
        try:
            lcd.clear()
        except:
            pass
    
    def print_statistics(self):
        """打印统计信息"""
        print(f"[FaceDetector] Session Statistics:")
        print(f"  Frames processed: {self.frame_count}")
        print(f"  Detections: {self.detection_count}")
        print(f"  Data sent: {self.send_count}")
        
        if self.frame_count > 0:
            detection_rate = self.detection_count / self.frame_count * 100
            print(f"  Detection rate: {detection_rate:.1f}%")
        
        # 打印通信统计
        if self.comm:
            self.comm.print_statistics()


def main():
    """主函数"""
    print("=== K230 Face Detection Example ===")
    
    # 创建人脸检测器
    detector = FaceDetector(uart_id=1, baudrate=115200)
    
    # 运行检测 (运行60秒，可以改为None无限运行)
    detector.run(duration=60)
    
    print("=== Face Detection Example Completed ===")


if __name__ == "__main__":
    main()
