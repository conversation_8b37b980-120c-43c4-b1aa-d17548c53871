#include "yb_protocol_app.h"

/* 私有函数声明 Private Function Declarations */
static void yb_app_color_handler(const yb_protocol_data_t* data);
static void yb_app_barcode_handler(const yb_protocol_data_t* data);
static void yb_app_qrcode_handler(const yb_protocol_data_t* data);
static void yb_app_apriltag_handler(const yb_protocol_data_t* data);
static void yb_app_face_detect_handler(const yb_protocol_data_t* data);
static void yb_app_person_detect_handler(const yb_protocol_data_t* data);
static void yb_app_hand_detect_handler(const yb_protocol_data_t* data);
static void yb_app_object_detect_handler(const yb_protocol_data_t* data);
static void yb_app_ocr_handler(const yb_protocol_data_t* data);
static void yb_app_gesture_handler(const yb_protocol_data_t* data);

/* 全局应用实例指针(用于回调函数访问) Global Application Instance Pointer */
static yb_protocol_app_t* g_app_instance = NULL;

/**
 * @brief 初始化应用层协议
 * @param app 应用层结构指针
 * @param comm 底层通信结构指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_init(yb_protocol_app_t* app, yb_uart_comm_t* comm)
{
    if (app == NULL || comm == NULL) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(app, 0, sizeof(yb_protocol_app_t));
    
    // 设置底层通信结构
    app->comm = comm;
    app->initialized = true;
    
    // 设置全局实例指针
    g_app_instance = app;
    
    YB_APP_DEBUG_PRINT("Application layer initialized");
    
    return HAL_OK;
}

/**
 * @brief 反初始化应用层协议
 * @param app 应用层结构指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_deinit(yb_protocol_app_t* app)
{
    if (app == NULL || !app->initialized) {
        return HAL_ERROR;
    }
    
    // 注销所有处理器
    if (app->comm != NULL) {
        yb_protocol_unregister_handler(app->comm, YB_ID_COLOR);
        yb_protocol_unregister_handler(app->comm, YB_ID_BARCODE);
        yb_protocol_unregister_handler(app->comm, YB_ID_QRCODE);
        yb_protocol_unregister_handler(app->comm, YB_ID_APRILTAG);
        yb_protocol_unregister_handler(app->comm, YB_ID_FACE_DETECT);
        yb_protocol_unregister_handler(app->comm, YB_ID_PERSON_DETECT);
        yb_protocol_unregister_handler(app->comm, YB_ID_HAND_DETECT);
        yb_protocol_unregister_handler(app->comm, YB_ID_OBJECT_DETECT);
        yb_protocol_unregister_handler(app->comm, YB_ID_OCR_REC);
        yb_protocol_unregister_handler(app->comm, YB_ID_HAND_GESTURE);
    }
    
    // 清零结构体
    memset(app, 0, sizeof(yb_protocol_app_t));
    
    // 清除全局实例指针
    if (g_app_instance == app) {
        g_app_instance = NULL;
    }
    
    YB_APP_DEBUG_PRINT("Application layer deinitialized");
    
    return HAL_OK;
}

/**
 * @brief 注册颜色识别回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_color_callback(yb_protocol_app_t* app, yb_color_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->color_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_COLOR, 
                                        yb_color_fields, 4, 
                                        yb_app_color_handler);
}

/**
 * @brief 注册条形码识别回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_barcode_callback(yb_protocol_app_t* app, yb_barcode_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->barcode_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_BARCODE,
                                        yb_barcode_fields, 5,
                                        yb_app_barcode_handler);
}

/**
 * @brief 注册二维码识别回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_qrcode_callback(yb_protocol_app_t* app, yb_qrcode_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->qrcode_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_QRCODE,
                                        yb_qrcode_fields, 5,
                                        yb_app_qrcode_handler);
}

/**
 * @brief 注册AprilTag识别回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_apriltag_callback(yb_protocol_app_t* app, yb_apriltag_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->apriltag_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_APRILTAG,
                                        yb_apriltag_fields, 6,
                                        yb_app_apriltag_handler);
}

/**
 * @brief 注册人脸检测回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_face_detect_callback(yb_protocol_app_t* app, yb_face_detect_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->face_detect_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_FACE_DETECT,
                                        yb_face_detect_fields, 4,
                                        yb_app_face_detect_handler);
}

/**
 * @brief 注册人体检测回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_person_detect_callback(yb_protocol_app_t* app, yb_person_detect_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->person_detect_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_PERSON_DETECT,
                                        yb_person_detect_fields, 4,
                                        yb_app_person_detect_handler);
}

/**
 * @brief 注册手掌检测回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_hand_detect_callback(yb_protocol_app_t* app, yb_hand_detect_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->hand_detect_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_HAND_DETECT,
                                        yb_hand_detect_fields, 4,
                                        yb_app_hand_detect_handler);
}

/**
 * @brief 注册物体检测回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_object_detect_callback(yb_protocol_app_t* app, yb_object_detect_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->object_detect_callback = callback;
    
    return yb_protocol_register_handler(app->comm, YB_ID_OBJECT_DETECT,
                                        yb_object_detect_fields, 5,
                                        yb_app_object_detect_handler);
}

/**
 * @brief 注册OCR识别回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_ocr_callback(yb_protocol_app_t* app, yb_ocr_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->ocr_callback = callback;
    
    // OCR只有一个字符串字段
    static const yb_field_meta_t ocr_fields[] = {{2, YB_FIELD_STRING}};
    
    return yb_protocol_register_handler(app->comm, YB_ID_OCR_REC,
                                        ocr_fields, 1,
                                        yb_app_ocr_handler);
}

/**
 * @brief 注册手势识别回调函数
 * @param app 应用层结构指针
 * @param callback 回调函数指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_register_gesture_callback(yb_protocol_app_t* app, yb_gesture_callback_t callback)
{
    if (app == NULL || !app->initialized || callback == NULL) {
        return HAL_ERROR;
    }
    
    app->gesture_callback = callback;
    
    // 手势识别只有一个字符串字段
    static const yb_field_meta_t gesture_fields[] = {{2, YB_FIELD_STRING}};
    
    return yb_protocol_register_handler(app->comm, YB_ID_HAND_GESTURE,
                                        gesture_fields, 1,
                                        yb_app_gesture_handler);
}

/**
 * @brief 处理协议数据
 * @param app 应用层结构指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_process(yb_protocol_app_t* app)
{
    if (app == NULL || !app->initialized || app->comm == NULL) {
        return HAL_ERROR;
    }
    
    return yb_protocol_process(app->comm);
}

/**
 * @brief 检查连接状态
 * @param app 应用层结构指针
 * @return 连接状态
 */
bool yb_app_is_connected(const yb_protocol_app_t* app)
{
    if (app == NULL || !app->initialized || app->comm == NULL) {
        return false;
    }
    
    return !yb_protocol_is_lost(app->comm);
}

/**
 * @brief 获取统计信息
 * @param app 应用层结构指针
 * @param rx_count 接收计数指针
 * @param success_count 成功计数指针
 * @param error_count 错误计数指针
 */
void yb_app_get_statistics(const yb_protocol_app_t* app, 
                           uint32_t* rx_count,
                           uint32_t* success_count,
                           uint32_t* error_count)
{
    if (app == NULL || !app->initialized || app->comm == NULL) {
        if (rx_count) *rx_count = 0;
        if (success_count) *success_count = 0;
        if (error_count) *error_count = 0;
        return;
    }
    
    yb_protocol_get_statistics(app->comm, rx_count, success_count, error_count);
}

/**
 * @brief 发送命令
 * @param app 应用层结构指针
 * @param command 命令字符串
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_send_command(yb_protocol_app_t* app, const char* command)
{
    if (app == NULL || !app->initialized || app->comm == NULL || command == NULL) {
        return HAL_ERROR;
    }

    return yb_protocol_send_string(app->comm, command);
}

/* 快速初始化函数 Quick Initialization Functions */

/**
 * @brief 快速初始化颜色追踪
 * @param app 应用层结构指针
 * @param comm 底层通信结构指针
 * @param callback 颜色回调函数
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_quick_init_color_tracking(yb_protocol_app_t* app,
                                                   yb_uart_comm_t* comm,
                                                   yb_color_callback_t callback)
{
    HAL_StatusTypeDef status;

    status = yb_app_init(app, comm);
    if (status != HAL_OK) {
        return status;
    }

    status = yb_app_register_color_callback(app, callback);
    if (status != HAL_OK) {
        yb_app_deinit(app);
        return status;
    }

    YB_APP_DEBUG_PRINT("Color tracking quick init completed");
    return HAL_OK;
}

/**
 * @brief 快速初始化人脸检测
 * @param app 应用层结构指针
 * @param comm 底层通信结构指针
 * @param callback 人脸检测回调函数
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_quick_init_face_detection(yb_protocol_app_t* app,
                                                   yb_uart_comm_t* comm,
                                                   yb_face_detect_callback_t callback)
{
    HAL_StatusTypeDef status;

    status = yb_app_init(app, comm);
    if (status != HAL_OK) {
        return status;
    }

    status = yb_app_register_face_detect_callback(app, callback);
    if (status != HAL_OK) {
        yb_app_deinit(app);
        return status;
    }

    YB_APP_DEBUG_PRINT("Face detection quick init completed");
    return HAL_OK;
}

/**
 * @brief 快速初始化二维码识别
 * @param app 应用层结构指针
 * @param comm 底层通信结构指针
 * @param callback 二维码回调函数
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_quick_init_qrcode_recognition(yb_protocol_app_t* app,
                                                       yb_uart_comm_t* comm,
                                                       yb_qrcode_callback_t callback)
{
    HAL_StatusTypeDef status;

    status = yb_app_init(app, comm);
    if (status != HAL_OK) {
        return status;
    }

    status = yb_app_register_qrcode_callback(app, callback);
    if (status != HAL_OK) {
        yb_app_deinit(app);
        return status;
    }

    YB_APP_DEBUG_PRINT("QRCode recognition quick init completed");
    return HAL_OK;
}

/**
 * @brief 快速初始化物体检测
 * @param app 应用层结构指针
 * @param comm 底层通信结构指针
 * @param callback 物体检测回调函数
 * @return HAL状态
 */
HAL_StatusTypeDef yb_app_quick_init_object_detection(yb_protocol_app_t* app,
                                                     yb_uart_comm_t* comm,
                                                     yb_object_detect_callback_t callback)
{
    HAL_StatusTypeDef status;

    status = yb_app_init(app, comm);
    if (status != HAL_OK) {
        return status;
    }

    status = yb_app_register_object_detect_callback(app, callback);
    if (status != HAL_OK) {
        yb_app_deinit(app);
        return status;
    }

    YB_APP_DEBUG_PRINT("Object detection quick init completed");
    return HAL_OK;
}

/* 私有处理函数实现 Private Handler Function Implementations */

/**
 * @brief 颜色识别处理函数
 * @param data 协议数据指针
 */
static void yb_app_color_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->color_callback == NULL || data == NULL) {
        return;
    }

    yb_color_data_t color_data;
    color_data.x = data->values[0];
    color_data.y = data->values[1];
    color_data.w = data->values[2];
    color_data.h = data->values[3];

    YB_APP_DEBUG_PRINT("Color detected: x=%d, y=%d, w=%d, h=%d",
                       color_data.x, color_data.y, color_data.w, color_data.h);

    g_app_instance->color_callback(&color_data);
}

/**
 * @brief 条形码识别处理函数
 * @param data 协议数据指针
 */
static void yb_app_barcode_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->barcode_callback == NULL || data == NULL) {
        return;
    }

    yb_code_data_t barcode_data;
    barcode_data.x = data->values[0];
    barcode_data.y = data->values[1];
    barcode_data.w = data->values[2];
    barcode_data.h = data->values[3];
    strncpy(barcode_data.message, data->strings[4], YB_PTO_BUF_LEN_MAX - 1);
    barcode_data.message[YB_PTO_BUF_LEN_MAX - 1] = '\0';

    YB_APP_DEBUG_PRINT("Barcode detected: x=%d, y=%d, w=%d, h=%d, msg=%s",
                       barcode_data.x, barcode_data.y, barcode_data.w, barcode_data.h, barcode_data.message);

    g_app_instance->barcode_callback(&barcode_data);
}

/**
 * @brief 二维码识别处理函数
 * @param data 协议数据指针
 */
static void yb_app_qrcode_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->qrcode_callback == NULL || data == NULL) {
        return;
    }

    yb_code_data_t qrcode_data;
    qrcode_data.x = data->values[0];
    qrcode_data.y = data->values[1];
    qrcode_data.w = data->values[2];
    qrcode_data.h = data->values[3];
    strncpy(qrcode_data.message, data->strings[4], YB_PTO_BUF_LEN_MAX - 1);
    qrcode_data.message[YB_PTO_BUF_LEN_MAX - 1] = '\0';

    YB_APP_DEBUG_PRINT("QRCode detected: x=%d, y=%d, w=%d, h=%d, msg=%s",
                       qrcode_data.x, qrcode_data.y, qrcode_data.w, qrcode_data.h, qrcode_data.message);

    g_app_instance->qrcode_callback(&qrcode_data);
}

/**
 * @brief AprilTag识别处理函数
 * @param data 协议数据指针
 */
static void yb_app_apriltag_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->apriltag_callback == NULL || data == NULL) {
        return;
    }

    yb_apriltag_data_t apriltag_data;
    apriltag_data.x = data->values[0];
    apriltag_data.y = data->values[1];
    apriltag_data.w = data->values[2];
    apriltag_data.h = data->values[3];
    apriltag_data.id = data->values[4];
    apriltag_data.degrees = data->values[5];

    YB_APP_DEBUG_PRINT("AprilTag detected: x=%d, y=%d, w=%d, h=%d, id=%d, degrees=%d",
                       apriltag_data.x, apriltag_data.y, apriltag_data.w, apriltag_data.h,
                       apriltag_data.id, apriltag_data.degrees);

    g_app_instance->apriltag_callback(&apriltag_data);
}

/**
 * @brief 人脸检测处理函数
 * @param data 协议数据指针
 */
static void yb_app_face_detect_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->face_detect_callback == NULL || data == NULL) {
        return;
    }

    yb_detection_data_t face_data;
    face_data.x = data->values[0];
    face_data.y = data->values[1];
    face_data.w = data->values[2];
    face_data.h = data->values[3];

    YB_APP_DEBUG_PRINT("Face detected: x=%d, y=%d, w=%d, h=%d",
                       face_data.x, face_data.y, face_data.w, face_data.h);

    g_app_instance->face_detect_callback(&face_data);
}

/**
 * @brief 人体检测处理函数
 * @param data 协议数据指针
 */
static void yb_app_person_detect_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->person_detect_callback == NULL || data == NULL) {
        return;
    }

    yb_detection_data_t person_data;
    person_data.x = data->values[0];
    person_data.y = data->values[1];
    person_data.w = data->values[2];
    person_data.h = data->values[3];

    YB_APP_DEBUG_PRINT("Person detected: x=%d, y=%d, w=%d, h=%d",
                       person_data.x, person_data.y, person_data.w, person_data.h);

    g_app_instance->person_detect_callback(&person_data);
}

/**
 * @brief 手掌检测处理函数
 * @param data 协议数据指针
 */
static void yb_app_hand_detect_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->hand_detect_callback == NULL || data == NULL) {
        return;
    }

    yb_detection_data_t hand_data;
    hand_data.x = data->values[0];
    hand_data.y = data->values[1];
    hand_data.w = data->values[2];
    hand_data.h = data->values[3];

    YB_APP_DEBUG_PRINT("Hand detected: x=%d, y=%d, w=%d, h=%d",
                       hand_data.x, hand_data.y, hand_data.w, hand_data.h);

    g_app_instance->hand_detect_callback(&hand_data);
}

/**
 * @brief 物体检测处理函数
 * @param data 协议数据指针
 */
static void yb_app_object_detect_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->object_detect_callback == NULL || data == NULL) {
        return;
    }

    yb_object_data_t object_data;
    object_data.x = data->values[0];
    object_data.y = data->values[1];
    object_data.w = data->values[2];
    object_data.h = data->values[3];
    strncpy(object_data.class_name, data->strings[4], YB_PTO_BUF_LEN_MAX - 1);
    object_data.class_name[YB_PTO_BUF_LEN_MAX - 1] = '\0';

    YB_APP_DEBUG_PRINT("Object detected: x=%d, y=%d, w=%d, h=%d, class=%s",
                       object_data.x, object_data.y, object_data.w, object_data.h, object_data.class_name);

    g_app_instance->object_detect_callback(&object_data);
}

/**
 * @brief OCR识别处理函数
 * @param data 协议数据指针
 */
static void yb_app_ocr_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->ocr_callback == NULL || data == NULL) {
        return;
    }

    YB_APP_DEBUG_PRINT("OCR text: %s", data->strings[0]);

    g_app_instance->ocr_callback(data->strings[0]);
}

/**
 * @brief 手势识别处理函数
 * @param data 协议数据指针
 */
static void yb_app_gesture_handler(const yb_protocol_data_t* data)
{
    if (g_app_instance == NULL || g_app_instance->gesture_callback == NULL || data == NULL) {
        return;
    }

    YB_APP_DEBUG_PRINT("Gesture: %s", data->strings[0]);

    g_app_instance->gesture_callback(data->strings[0]);
}
