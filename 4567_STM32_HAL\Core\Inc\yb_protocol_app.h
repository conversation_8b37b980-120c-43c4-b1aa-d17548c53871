#ifndef _YB_PROTOCOL_APP_H_
#define _YB_PROTOCOL_APP_H_

#include "yb_protocol_hal.h"

/* 应用层数据结构 Application Layer Data Structures */

/* 颜色识别数据 Color Detection Data */
typedef struct {
    int x, y, w, h;  // 位置和尺寸
} yb_color_data_t;

/* 条形码/二维码数据 Barcode/QRCode Data */
typedef struct {
    int x, y, w, h;              // 位置和尺寸
    char message[YB_PTO_BUF_LEN_MAX];  // 识别内容
} yb_code_data_t;

/* AprilTag数据 AprilTag Data */
typedef struct {
    int x, y, w, h;    // 位置和尺寸
    int id;            // 标签ID
    int degrees;       // 角度
} yb_apriltag_data_t;

/* 人脸/人体/手掌检测数据 Face/Person/Hand Detection Data */
typedef struct {
    int x, y, w, h;  // 位置和尺寸
} yb_detection_data_t;

/* 物体检测数据 Object Detection Data */
typedef struct {
    int x, y, w, h;                    // 位置和尺寸
    char class_name[YB_PTO_BUF_LEN_MAX];    // 类别名称
} yb_object_data_t;

/* 应用层回调函数类型 Application Layer Callback Function Types */
typedef void (*yb_color_callback_t)(const yb_color_data_t* data);
typedef void (*yb_barcode_callback_t)(const yb_code_data_t* data);
typedef void (*yb_qrcode_callback_t)(const yb_code_data_t* data);
typedef void (*yb_apriltag_callback_t)(const yb_apriltag_data_t* data);
typedef void (*yb_face_detect_callback_t)(const yb_detection_data_t* data);
typedef void (*yb_person_detect_callback_t)(const yb_detection_data_t* data);
typedef void (*yb_hand_detect_callback_t)(const yb_detection_data_t* data);
typedef void (*yb_object_detect_callback_t)(const yb_object_data_t* data);
typedef void (*yb_ocr_callback_t)(const char* text);
typedef void (*yb_gesture_callback_t)(const char* gesture);

/* 应用层控制结构 Application Layer Control Structure */
typedef struct {
    yb_uart_comm_t* comm;                           // 底层通信结构
    
    // 应用层回调函数
    yb_color_callback_t color_callback;
    yb_barcode_callback_t barcode_callback;
    yb_qrcode_callback_t qrcode_callback;
    yb_apriltag_callback_t apriltag_callback;
    yb_face_detect_callback_t face_detect_callback;
    yb_person_detect_callback_t person_detect_callback;
    yb_hand_detect_callback_t hand_detect_callback;
    yb_object_detect_callback_t object_detect_callback;
    yb_ocr_callback_t ocr_callback;
    yb_gesture_callback_t gesture_callback;
    
    // 状态标志
    bool initialized;
} yb_protocol_app_t;

/* 应用层函数声明 Application Layer Function Declarations */

/* 初始化和配置 Initialization and Configuration */
HAL_StatusTypeDef yb_app_init(yb_protocol_app_t* app, yb_uart_comm_t* comm);
HAL_StatusTypeDef yb_app_deinit(yb_protocol_app_t* app);

/* 回调函数注册 Callback Function Registration */
HAL_StatusTypeDef yb_app_register_color_callback(yb_protocol_app_t* app, yb_color_callback_t callback);
HAL_StatusTypeDef yb_app_register_barcode_callback(yb_protocol_app_t* app, yb_barcode_callback_t callback);
HAL_StatusTypeDef yb_app_register_qrcode_callback(yb_protocol_app_t* app, yb_qrcode_callback_t callback);
HAL_StatusTypeDef yb_app_register_apriltag_callback(yb_protocol_app_t* app, yb_apriltag_callback_t callback);
HAL_StatusTypeDef yb_app_register_face_detect_callback(yb_protocol_app_t* app, yb_face_detect_callback_t callback);
HAL_StatusTypeDef yb_app_register_person_detect_callback(yb_protocol_app_t* app, yb_person_detect_callback_t callback);
HAL_StatusTypeDef yb_app_register_hand_detect_callback(yb_protocol_app_t* app, yb_hand_detect_callback_t callback);
HAL_StatusTypeDef yb_app_register_object_detect_callback(yb_protocol_app_t* app, yb_object_detect_callback_t callback);
HAL_StatusTypeDef yb_app_register_ocr_callback(yb_protocol_app_t* app, yb_ocr_callback_t callback);
HAL_StatusTypeDef yb_app_register_gesture_callback(yb_protocol_app_t* app, yb_gesture_callback_t callback);

/* 数据处理 Data Processing */
HAL_StatusTypeDef yb_app_process(yb_protocol_app_t* app);

/* 状态查询 Status Query */
bool yb_app_is_connected(const yb_protocol_app_t* app);
void yb_app_get_statistics(const yb_protocol_app_t* app, 
                           uint32_t* rx_count,
                           uint32_t* success_count,
                           uint32_t* error_count);

/* 便捷函数 Convenience Functions */
HAL_StatusTypeDef yb_app_send_command(yb_protocol_app_t* app, const char* command);

/* 预定义的快速初始化函数 Predefined Quick Initialization Functions */
HAL_StatusTypeDef yb_app_quick_init_color_tracking(yb_protocol_app_t* app, 
                                                   yb_uart_comm_t* comm,
                                                   yb_color_callback_t callback);

HAL_StatusTypeDef yb_app_quick_init_face_detection(yb_protocol_app_t* app,
                                                   yb_uart_comm_t* comm,
                                                   yb_face_detect_callback_t callback);

HAL_StatusTypeDef yb_app_quick_init_qrcode_recognition(yb_protocol_app_t* app,
                                                       yb_uart_comm_t* comm,
                                                       yb_qrcode_callback_t callback);

HAL_StatusTypeDef yb_app_quick_init_object_detection(yb_protocol_app_t* app,
                                                     yb_uart_comm_t* comm,
                                                     yb_object_detect_callback_t callback);

/* 工具宏定义 Utility Macros */
#define YB_APP_INIT_STRUCT(app) memset((app), 0, sizeof(yb_protocol_app_t))

/* 调试宏 Debug Macros */
#ifdef YB_PROTOCOL_DEBUG
#define YB_APP_DEBUG_PRINT(fmt, ...) printf("[YB_APP] " fmt "\r\n", ##__VA_ARGS__)
#else
#define YB_APP_DEBUG_PRINT(fmt, ...)
#endif

/* 错误码定义 Error Code Definitions */
typedef enum {
    YB_APP_OK = 0,
    YB_APP_ERROR = 1,
    YB_APP_ERROR_NULL_POINTER = 2,
    YB_APP_ERROR_NOT_INITIALIZED = 3,
    YB_APP_ERROR_ALREADY_REGISTERED = 4,
    YB_APP_ERROR_COMM_FAILED = 5
} yb_app_error_t;

#endif /* _YB_PROTOCOL_APP_H_ */
