"""
YbUart - K230串口通信模块
基于workplace工程的串口实现，为4567工程提供串口通信功能

作者: 4567工程团队
日期: 2024
版本: 1.0.0
"""

import time
from machine import UART


class YbUart:
    """
    YB串口通信类
    提供串口初始化、数据发送、接收等基础功能
    """
    
    def __init__(self, uart_id=1, baudrate=115200, bits=8, parity=None, stop=1, timeout=1000, read_buf_len=4096):
        """
        初始化串口
        
        Args:
            uart_id: UART端口号，默认为1
            baudrate: 波特率，默认115200
            bits: 数据位，默认8位
            parity: 校验位，默认无校验
            stop: 停止位，默认1位
            timeout: 超时时间(毫秒)，默认1000ms
            read_buf_len: 读缓冲区长度，默认4096字节
        """
        self.uart_id = uart_id
        self.baudrate = baudrate
        self.bits = bits
        self.parity = parity
        self.stop = stop
        self.timeout = timeout
        self.read_buf_len = read_buf_len
        
        # 初始化UART硬件
        try:
            self.uart = UART(uart_id, baudrate, bits, parity, stop, 
                           timeout=timeout, read_buf_len=read_buf_len)
            self.initialized = True
            print(f"[YbUart] UART{uart_id} initialized: {baudrate} baud, {bits}{parity if parity else 'N'}{stop}")
        except Exception as e:
            print(f"[YbUart] UART initialization failed: {e}")
            self.initialized = False
            self.uart = None
        
        # 统计信息
        self.tx_count = 0
        self.rx_count = 0
        self.tx_bytes = 0
        self.rx_bytes = 0
        self.error_count = 0
        
        # 状态标志
        self.last_tx_time = 0
        self.last_rx_time = 0
    
    def is_initialized(self):
        """检查串口是否初始化成功"""
        return self.initialized and self.uart is not None
    
    def send(self, data):
        """
        发送数据
        
        Args:
            data: 要发送的数据，可以是字符串或字节数组
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_initialized():
            print("[YbUart] UART not initialized")
            return False
        
        try:
            if isinstance(data, str):
                # 字符串转换为字节
                data_bytes = data.encode('utf-8')
            elif isinstance(data, (bytes, bytearray)):
                data_bytes = data
            else:
                # 其他类型转换为字符串再编码
                data_bytes = str(data).encode('utf-8')
            
            # 发送数据
            bytes_sent = self.uart.write(data_bytes)
            
            if bytes_sent:
                self.tx_count += 1
                self.tx_bytes += len(data_bytes)
                self.last_tx_time = time.ticks_ms()
                return True
            else:
                self.error_count += 1
                return False
                
        except Exception as e:
            print(f"[YbUart] Send error: {e}")
            self.error_count += 1
            return False
    
    def send_string(self, string):
        """
        发送字符串
        
        Args:
            string: 要发送的字符串
            
        Returns:
            bool: 发送是否成功
        """
        return self.send(string)
    
    def send_bytes(self, data_bytes):
        """
        发送字节数组
        
        Args:
            data_bytes: 要发送的字节数组
            
        Returns:
            bool: 发送是否成功
        """
        return self.send(data_bytes)
    
    def read(self, size=None):
        """
        读取数据
        
        Args:
            size: 要读取的字节数，None表示读取所有可用数据
            
        Returns:
            bytes: 读取到的数据，如果没有数据返回None
        """
        if not self.is_initialized():
            return None
        
        try:
            if self.uart.any():
                data = self.uart.read(size)
                if data:
                    self.rx_count += 1
                    self.rx_bytes += len(data)
                    self.last_rx_time = time.ticks_ms()
                return data
            return None
        except Exception as e:
            print(f"[YbUart] Read error: {e}")
            self.error_count += 1
            return None
    
    def read_string(self, size=None, encoding='utf-8'):
        """
        读取字符串
        
        Args:
            size: 要读取的字节数
            encoding: 字符编码，默认utf-8
            
        Returns:
            str: 读取到的字符串，如果没有数据返回None
        """
        data = self.read(size)
        if data:
            try:
                return data.decode(encoding)
            except Exception as e:
                print(f"[YbUart] String decode error: {e}")
                return None
        return None
    
    def readline(self, encoding='utf-8'):
        """
        读取一行数据
        
        Args:
            encoding: 字符编码，默认utf-8
            
        Returns:
            str: 读取到的一行字符串，如果没有数据返回None
        """
        if not self.is_initialized():
            return None
        
        try:
            data = self.uart.readline()
            if data:
                self.rx_count += 1
                self.rx_bytes += len(data)
                self.last_rx_time = time.ticks_ms()
                return data.decode(encoding).strip()
            return None
        except Exception as e:
            print(f"[YbUart] Readline error: {e}")
            self.error_count += 1
            return None
    
    def any(self):
        """
        检查是否有可读数据
        
        Returns:
            int: 可读字节数，0表示没有数据
        """
        if not self.is_initialized():
            return 0
        
        try:
            return self.uart.any()
        except Exception as e:
            print(f"[YbUart] Any error: {e}")
            return 0
    
    def flush(self):
        """清空发送缓冲区"""
        if not self.is_initialized():
            return False
        
        try:
            self.uart.flush()
            return True
        except Exception as e:
            print(f"[YbUart] Flush error: {e}")
            return False
    
    def clear_rx_buffer(self):
        """清空接收缓冲区"""
        if not self.is_initialized():
            return False
        
        try:
            # 读取并丢弃所有数据
            while self.uart.any():
                self.uart.read()
            return True
        except Exception as e:
            print(f"[YbUart] Clear RX buffer error: {e}")
            return False
    
    def set_timeout(self, timeout_ms):
        """
        设置超时时间
        
        Args:
            timeout_ms: 超时时间(毫秒)
        """
        self.timeout = timeout_ms
        # 注意：machine.UART可能不支持动态修改超时时间
        print(f"[YbUart] Timeout set to {timeout_ms}ms (may require restart)")
    
    def get_statistics(self):
        """
        获取统计信息
        
        Returns:
            dict: 包含统计信息的字典
        """
        return {
            'uart_id': self.uart_id,
            'baudrate': self.baudrate,
            'initialized': self.initialized,
            'tx_count': self.tx_count,
            'rx_count': self.rx_count,
            'tx_bytes': self.tx_bytes,
            'rx_bytes': self.rx_bytes,
            'error_count': self.error_count,
            'last_tx_time': self.last_tx_time,
            'last_rx_time': self.last_rx_time
        }
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        print(f"[YbUart] Statistics for UART{self.uart_id}:")
        print(f"  Baudrate: {stats['baudrate']}")
        print(f"  Initialized: {stats['initialized']}")
        print(f"  TX Count: {stats['tx_count']}, TX Bytes: {stats['tx_bytes']}")
        print(f"  RX Count: {stats['rx_count']}, RX Bytes: {stats['rx_bytes']}")
        print(f"  Error Count: {stats['error_count']}")
        print(f"  Last TX: {stats['last_tx_time']}ms")
        print(f"  Last RX: {stats['last_rx_time']}ms")
    
    def reset_statistics(self):
        """重置统计信息"""
        self.tx_count = 0
        self.rx_count = 0
        self.tx_bytes = 0
        self.rx_bytes = 0
        self.error_count = 0
        self.last_tx_time = 0
        self.last_rx_time = 0
        print("[YbUart] Statistics reset")
    
    def deinit(self):
        """反初始化串口"""
        if self.uart:
            try:
                self.uart.deinit()
                print(f"[YbUart] UART{self.uart_id} deinitialized")
            except Exception as e:
                print(f"[YbUart] Deinit error: {e}")
            finally:
                self.uart = None
                self.initialized = False
    
    def __del__(self):
        """析构函数"""
        self.deinit()


class YbUartConfig:
    """YB串口配置类"""
    
    # 常用波特率
    BAUDRATE_9600 = 9600
    BAUDRATE_19200 = 19200
    BAUDRATE_38400 = 38400
    BAUDRATE_57600 = 57600
    BAUDRATE_115200 = 115200
    BAUDRATE_230400 = 230400
    BAUDRATE_460800 = 460800
    BAUDRATE_921600 = 921600
    
    # 默认配置
    DEFAULT_UART_ID = 1
    DEFAULT_BAUDRATE = BAUDRATE_115200
    DEFAULT_BITS = 8
    DEFAULT_PARITY = None
    DEFAULT_STOP = 1
    DEFAULT_TIMEOUT = 1000
    DEFAULT_READ_BUF_LEN = 4096
    
    @classmethod
    def get_default_config(cls):
        """获取默认配置"""
        return {
            'uart_id': cls.DEFAULT_UART_ID,
            'baudrate': cls.DEFAULT_BAUDRATE,
            'bits': cls.DEFAULT_BITS,
            'parity': cls.DEFAULT_PARITY,
            'stop': cls.DEFAULT_STOP,
            'timeout': cls.DEFAULT_TIMEOUT,
            'read_buf_len': cls.DEFAULT_READ_BUF_LEN
        }
    
    @classmethod
    def create_uart_with_config(cls, config=None):
        """
        使用配置创建串口实例
        
        Args:
            config: 配置字典，None使用默认配置
            
        Returns:
            YbUart: 串口实例
        """
        if config is None:
            config = cls.get_default_config()
        
        return YbUart(**config)


# 便捷函数
def create_uart(uart_id=1, baudrate=115200):
    """
    创建串口实例的便捷函数
    
    Args:
        uart_id: UART端口号
        baudrate: 波特率
        
    Returns:
        YbUart: 串口实例
    """
    return YbUart(uart_id=uart_id, baudrate=baudrate)


def test_uart_loopback(uart_id=1, baudrate=115200):
    """
    串口回环测试
    
    Args:
        uart_id: UART端口号
        baudrate: 波特率
    """
    print(f"[YbUart] Starting loopback test on UART{uart_id}")
    
    uart = YbUart(uart_id=uart_id, baudrate=baudrate)
    
    if not uart.is_initialized():
        print("[YbUart] Test failed: UART not initialized")
        return
    
    test_data = "Hello, YbUart!"
    print(f"[YbUart] Sending: {test_data}")
    
    if uart.send(test_data):
        print("[YbUart] Send successful")
        
        # 等待数据
        time.sleep(0.1)
        
        # 尝试读取
        received = uart.read_string()
        if received:
            print(f"[YbUart] Received: {received}")
            if received == test_data:
                print("[YbUart] Loopback test PASSED")
            else:
                print("[YbUart] Loopback test FAILED: data mismatch")
        else:
            print("[YbUart] Loopback test FAILED: no data received")
    else:
        print("[YbUart] Test failed: send error")
    
    uart.print_statistics()
    uart.deinit()


if __name__ == "__main__":
    # 测试代码
    print("YbUart module test")
    test_uart_loopback()
