..\obj\ioi2c.o: ..\BSP\Motor\IOI2C.c
..\obj\ioi2c.o: ..\BSP\Motor\ioi2c.h
..\obj\ioi2c.o: ..\USER\AllHeader.h
..\obj\ioi2c.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\ioi2c.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\ioi2c.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
..\obj\ioi2c.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\ioi2c.o: ..\CMSIS\stm32f10x.h
..\obj\ioi2c.o: ..\CMSIS\core_cm3.h
..\obj\ioi2c.o: ..\CMSIS\system_stm32f10x.h
..\obj\ioi2c.o: ..\CMSIS\stm32f10x.h
..\obj\ioi2c.o: ..\USER\stm32f10x_conf.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_adc.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_bkp.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_can.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_cec.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_crc.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_dac.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_dbgmcu.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_dma.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_exti.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_flash.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_fsmc.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_gpio.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_i2c.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_iwdg.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_pwr.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_rcc.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_rtc.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_sdio.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_spi.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_tim.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_usart.h
..\obj\ioi2c.o: ..\FWLib\inc\stm32f10x_wwdg.h
..\obj\ioi2c.o: ..\FWLib\inc\misc.h
..\obj\ioi2c.o: ..\BSP\bsp.h
..\obj\ioi2c.o: ..\USER\AllHeader.h
..\obj\ioi2c.o: ..\BSP\bsp_common.h
..\obj\ioi2c.o: ..\BSP\delay.h
..\obj\ioi2c.o: ..\BSP\UART\bsp_usart.h
..\obj\ioi2c.o: ..\APP\app_motor.h
..\obj\ioi2c.o: ..\BSP\Motor\bsp_motor_iic.h
..\obj\ioi2c.o: ..\BSP\Motor\IOI2C.h
..\obj\ioi2c.o: ..\BSP\RGB\bsp_rgb.h
..\obj\ioi2c.o: ..\APP\yb_protocol.h
