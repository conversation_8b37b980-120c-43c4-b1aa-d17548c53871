"""
二维码识别示例 - K230端
演示如何使用YbComm进行二维码识别数据通信

作者: 4567工程团队
日期: 2024
"""

import time
import sensor
import image
import lcd
from libs.YbComm import YbComm


class QRCodeRecognizer:
    """二维码识别器"""
    
    def __init__(self, uart_id=1, baudrate=115200):
        """
        初始化二维码识别器
        
        Args:
            uart_id: UART端口号
            baudrate: 波特率
        """
        # 初始化通信
        self.comm = YbComm(uart_id=uart_id, baudrate=baudrate)
        
        # 统计信息
        self.frame_count = 0
        self.detection_count = 0
        self.send_count = 0
        self.last_qrcode = None
        self.last_send_time = 0
        
        print("[QRCodeRecognizer] QR Code recognizer initialized")
    
    def init_camera(self):
        """初始化摄像头"""
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)  # 320x240
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            
            # 初始化LCD显示
            lcd.init()
            lcd.clear()
            
            print("[QRCodeRecognizer] Camera initialized")
            return True
        except Exception as e:
            print(f"[QRCodeRecognizer] Camera init error: {e}")
            return False
    
    def detect_qrcode(self, img):
        """
        检测二维码
        
        Args:
            img: 图像对象
            
        Returns:
            list: 检测到的二维码列表
        """
        try:
            # 查找二维码
            qrcodes = img.find_qrcodes()
            return qrcodes
        except Exception as e:
            print(f"[QRCodeRecognizer] QR detection error: {e}")
            return []
    
    def process_frame(self):
        """处理一帧图像"""
        try:
            # 获取图像
            img = sensor.snapshot()
            self.frame_count += 1
            
            # 检测二维码
            qrcodes = self.detect_qrcode(img)
            
            if qrcodes:
                for qrcode in qrcodes:
                    # 获取位置和内容信息
                    x = qrcode.x()
                    y = qrcode.y()
                    w = qrcode.w()
                    h = qrcode.h()
                    message = qrcode.payload()
                    
                    # 绘制检测框
                    img.draw_rectangle(qrcode.rect(), color=(0, 255, 0))
                    
                    # 显示二维码内容
                    content_text = f"QR: {message}"
                    img.draw_string(10, 10, content_text, color=(255, 255, 255))
                    
                    # 显示位置信息
                    pos_text = f"Pos: ({x},{y}) {w}x{h}"
                    img.draw_string(10, 30, pos_text, color=(255, 255, 255))
                    
                    # 避免重复发送相同内容
                    current_time = time.ticks_ms()
                    if (self.last_qrcode != message or 
                        time.ticks_diff(current_time, self.last_send_time) > 1000):  # 1秒间隔
                        
                        # 发送数据
                        if self.comm.send_qrcode_data(x, y, w, h, message):
                            self.send_count += 1
                            self.last_qrcode = message
                            self.last_send_time = current_time
                            print(f"[QRCodeRecognizer] QR data sent: {message} at ({x},{y})")
                    
                    self.detection_count += 1
                    break  # 只处理第一个二维码
            else:
                # 未检测到二维码
                img.draw_string(10, 10, "No QR code detected", color=(255, 255, 255))
            
            # 显示统计信息
            stats_text = f"F:{self.frame_count} D:{self.detection_count} S:{self.send_count}"
            img.draw_string(10, 220, stats_text, color=(255, 255, 0))
            
            # 显示图像
            lcd.display(img)
            
            return len(qrcodes) > 0
            
        except Exception as e:
            print(f"[QRCodeRecognizer] Process frame error: {e}")
            return False
    
    def run(self, duration=None):
        """
        运行二维码识别
        
        Args:
            duration: 运行时长(秒)，None表示无限运行
        """
        if not self.comm.is_initialized():
            print("[QRCodeRecognizer] Communication not initialized")
            return
        
        if not self.init_camera():
            print("[QRCodeRecognizer] Camera initialization failed")
            return
        
        print("[QRCodeRecognizer] Starting QR code recognition")
        
        start_time = time.ticks_ms()
        
        try:
            while True:
                # 处理一帧
                self.process_frame()
                
                # 检查运行时长
                if duration:
                    elapsed = time.ticks_diff(time.ticks_ms(), start_time) / 1000
                    if elapsed >= duration:
                        break
                
                # 短暂延时
                time.sleep(0.1)  # 10fps
                
        except KeyboardInterrupt:
            print("[QRCodeRecognizer] Stopped by user")
        except Exception as e:
            print("[QRCodeRecognizer] Runtime error: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("[QRCodeRecognizer] Cleaning up...")
        
        # 打印统计信息
        self.print_statistics()
        
        # 清理通信
        if self.comm:
            self.comm.deinit()
        
        # 清理显示
        try:
            lcd.clear()
        except:
            pass
    
    def print_statistics(self):
        """打印统计信息"""
        print(f"[QRCodeRecognizer] Session Statistics:")
        print(f"  Frames processed: {self.frame_count}")
        print(f"  Detections: {self.detection_count}")
        print(f"  Data sent: {self.send_count}")
        print(f"  Last QR code: {self.last_qrcode}")
        
        if self.frame_count > 0:
            detection_rate = self.detection_count / self.frame_count * 100
            print(f"  Detection rate: {detection_rate:.1f}%")
        
        # 打印通信统计
        if self.comm:
            self.comm.print_statistics()


def main():
    """主函数"""
    print("=== K230 QR Code Recognition Example ===")
    
    # 创建二维码识别器
    recognizer = QRCodeRecognizer(uart_id=1, baudrate=115200)
    
    # 运行识别 (运行60秒，可以改为None无限运行)
    recognizer.run(duration=60)
    
    print("=== QR Code Recognition Example Completed ===")


if __name__ == "__main__":
    main()
