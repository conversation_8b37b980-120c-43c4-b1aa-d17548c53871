#include "yb_protocol_hal.h"

/* 预定义字段元数据 Predefined Field Metadata */
const yb_field_meta_t yb_color_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}  // x,y,w,h
};

const yb_field_meta_t yb_barcode_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}, {6, YB_FIELD_STRING}  // x,y,w,h,msg
};

const yb_field_meta_t yb_qrcode_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}, {6, YB_FIELD_STRING}  // x,y,w,h,msg
};

const yb_field_meta_t yb_apriltag_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}, {6, YB_FIELD_INT}, {7, YB_FIELD_INT}  // x,y,w,h,id,degrees
};

const yb_field_meta_t yb_face_detect_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}  // x,y,w,h
};

const yb_field_meta_t yb_person_detect_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}  // x,y,w,h
};

const yb_field_meta_t yb_hand_detect_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}  // x,y,w,h
};

const yb_field_meta_t yb_object_detect_fields[] = {
    {2, YB_FIELD_INT}, {3, YB_FIELD_INT}, {4, YB_FIELD_INT}, {5, YB_FIELD_INT}, {6, YB_FIELD_STRING}  // x,y,w,h,class
};

/* 私有函数声明 Private Function Declarations */
static HAL_StatusTypeDef yb_protocol_parse_data(yb_uart_comm_t* comm, uint8_t* data_buf, uint16_t length);
static yb_func_desc_t* yb_protocol_find_handler(yb_uart_comm_t* comm, yb_protocol_id_t id);
static void yb_protocol_reset_rx_state(yb_uart_comm_t* comm);

/**
 * @brief 初始化协议通信
 * @param comm 通信结构指针
 * @param huart HAL库UART句柄
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_init(yb_uart_comm_t* comm, UART_HandleTypeDef* huart)
{
    if (comm == NULL || huart == NULL) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(comm, 0, sizeof(yb_uart_comm_t));
    
    // 初始化基本参数
    comm->huart = huart;
    comm->protocol.state = YB_PTO_STATE_IDLE;
    comm->protocol.timeout_ms = 1000; // 默认1秒超时
    comm->initialized = true;
    
    // 启动UART接收中断
    HAL_UART_Receive_IT(huart, &comm->protocol.rx_buffer[0], 1);
    
    return HAL_OK;
}

/**
 * @brief 反初始化协议通信
 * @param comm 通信结构指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_deinit(yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return HAL_ERROR;
    }
    
    // 停止UART接收
    HAL_UART_AbortReceive_IT(comm->huart);
    
    // 清零结构体
    memset(comm, 0, sizeof(yb_uart_comm_t));
    
    return HAL_OK;
}

/**
 * @brief 设置超时时间
 * @param comm 通信结构指针
 * @param timeout_ms 超时时间(毫秒)
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_set_timeout(yb_uart_comm_t* comm, uint32_t timeout_ms)
{
    if (comm == NULL || !comm->initialized) {
        return HAL_ERROR;
    }
    
    comm->protocol.timeout_ms = timeout_ms;
    return HAL_OK;
}

/**
 * @brief 注册协议处理器
 * @param comm 通信结构指针
 * @param id 协议ID
 * @param fields 字段元数据数组
 * @param field_count 字段数量
 * @param handler 处理函数
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_register_handler(yb_uart_comm_t* comm, 
                                               yb_protocol_id_t id,
                                               const yb_field_meta_t* fields,
                                               uint8_t field_count,
                                               yb_protocol_handler_t handler)
{
    if (comm == NULL || !comm->initialized || fields == NULL || handler == NULL) {
        return HAL_ERROR;
    }
    
    if (comm->protocol.handler_count >= YB_PTO_MAX_HANDLERS) {
        return HAL_ERROR; // 处理器数量已满
    }
    
    // 检查是否已存在相同ID的处理器
    if (yb_protocol_find_handler(comm, id) != NULL) {
        return HAL_ERROR; // 已存在
    }
    
    // 添加新处理器
    yb_func_desc_t* desc = &comm->protocol.handlers[comm->protocol.handler_count];
    desc->id = id;
    desc->fields = fields;
    desc->field_count = field_count;
    desc->handler = handler;
    
    comm->protocol.handler_count++;
    
    return HAL_OK;
}

/**
 * @brief 注销协议处理器
 * @param comm 通信结构指针
 * @param id 协议ID
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_unregister_handler(yb_uart_comm_t* comm, yb_protocol_id_t id)
{
    if (comm == NULL || !comm->initialized) {
        return HAL_ERROR;
    }
    
    // 查找处理器
    for (uint8_t i = 0; i < comm->protocol.handler_count; i++) {
        if (comm->protocol.handlers[i].id == id) {
            // 移动后续元素
            for (uint8_t j = i; j < comm->protocol.handler_count - 1; j++) {
                comm->protocol.handlers[j] = comm->protocol.handlers[j + 1];
            }
            comm->protocol.handler_count--;
            return HAL_OK;
        }
    }
    
    return HAL_ERROR; // 未找到
}

/**
 * @brief 处理接收到的字节
 * @param comm 通信结构指针
 * @param byte 接收到的字节
 */
void yb_protocol_rx_byte(yb_uart_comm_t* comm, uint8_t byte)
{
    if (comm == NULL || !comm->initialized) {
        return;
    }
    
    yb_protocol_ctrl_t* pto = &comm->protocol;
    
    switch (pto->state) {
        case YB_PTO_STATE_IDLE:
            if (byte == YB_PTO_HEAD) {
                pto->rx_buffer[0] = YB_PTO_HEAD;
                pto->rx_index = 1;
                pto->state = YB_PTO_STATE_RECEIVING;
                pto->last_rx_time = yb_protocol_get_tick();
            }
            break;
            
        case YB_PTO_STATE_RECEIVING:
            if (pto->rx_index < YB_PTO_BUF_LEN_MAX) {
                pto->rx_buffer[pto->rx_index] = byte;
                pto->rx_index++;
                pto->last_rx_time = yb_protocol_get_tick();
                
                if (byte == YB_PTO_TAIL) {
                    pto->rx_length = pto->rx_index;
                    pto->state = YB_PTO_STATE_READY;
                    pto->new_data_flag = true;
                    pto->rx_count++;
                }
            } else {
                // 缓冲区溢出，重置状态
                yb_protocol_reset_rx_state(comm);
            }
            break;
            
        case YB_PTO_STATE_READY:
            // 在数据处理完成前忽略新数据
            break;
    }
}

/**
 * @brief 处理协议数据
 * @param comm 通信结构指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_process(yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return HAL_ERROR;
    }
    
    yb_protocol_ctrl_t* pto = &comm->protocol;
    
    // 检查超时
    if (pto->state == YB_PTO_STATE_RECEIVING) {
        uint32_t current_time = yb_protocol_get_tick();
        if (current_time - pto->last_rx_time > pto->timeout_ms) {
            yb_protocol_reset_rx_state(comm);
            pto->lost_flag = true;
        }
    }
    
    // 处理新数据
    if (pto->new_data_flag && pto->state == YB_PTO_STATE_READY) {
        HAL_StatusTypeDef result = yb_protocol_parse_data(comm, pto->rx_buffer, pto->rx_length);
        
        if (result == HAL_OK) {
            pto->parse_success_count++;
            pto->lost_flag = false;
        } else {
            pto->parse_error_count++;
        }
        
        // 重置状态
        pto->new_data_flag = false;
        pto->state = YB_PTO_STATE_IDLE;
        pto->rx_index = 0;
        
        return result;
    }
    
    return HAL_OK;
}

/**
 * @brief 清除接收缓冲区
 * @param comm 通信结构指针
 */
void yb_protocol_clear_buffer(yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return;
    }
    
    yb_protocol_reset_rx_state(comm);
}

/**
 * @brief 发送数据
 * @param comm 通信结构指针
 * @param data 数据指针
 * @param length 数据长度
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_send_data(yb_uart_comm_t* comm, const uint8_t* data, uint16_t length)
{
    if (comm == NULL || !comm->initialized || data == NULL || length == 0) {
        return HAL_ERROR;
    }
    
    return HAL_UART_Transmit(comm->huart, (uint8_t*)data, length, 1000);
}

/**
 * @brief 发送字符串
 * @param comm 通信结构指针
 * @param str 字符串指针
 * @return HAL状态
 */
HAL_StatusTypeDef yb_protocol_send_string(yb_uart_comm_t* comm, const char* str)
{
    if (comm == NULL || !comm->initialized || str == NULL) {
        return HAL_ERROR;
    }

    uint16_t length = strlen(str);
    return yb_protocol_send_data(comm, (const uint8_t*)str, length);
}

/**
 * @brief 检查是否有新数据就绪
 * @param comm 通信结构指针
 * @return 数据就绪状态
 */
bool yb_protocol_is_data_ready(const yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return false;
    }

    return comm->protocol.new_data_flag;
}

/**
 * @brief 检查通信是否丢失
 * @param comm 通信结构指针
 * @return 丢失状态
 */
bool yb_protocol_is_lost(const yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return true;
    }

    return comm->protocol.lost_flag;
}

/**
 * @brief 获取统计信息
 * @param comm 通信结构指针
 * @param rx_count 接收计数指针
 * @param success_count 成功计数指针
 * @param error_count 错误计数指针
 */
void yb_protocol_get_statistics(const yb_uart_comm_t* comm,
                                uint32_t* rx_count,
                                uint32_t* success_count,
                                uint32_t* error_count)
{
    if (comm == NULL || !comm->initialized) {
        if (rx_count) *rx_count = 0;
        if (success_count) *success_count = 0;
        if (error_count) *error_count = 0;
        return;
    }

    if (rx_count) *rx_count = comm->protocol.rx_count;
    if (success_count) *success_count = comm->protocol.parse_success_count;
    if (error_count) *error_count = comm->protocol.parse_error_count;
}

/**
 * @brief 字符串转整数
 * @param str 字符串指针
 * @return 整数值
 */
int yb_protocol_char_to_int(const char* str)
{
    if (str == NULL) {
        return 0;
    }

    int result = 0;
    int sign = 1;

    // 处理符号
    if (*str == '-') {
        sign = -1;
        str++;
    } else if (*str == '+') {
        str++;
    }

    // 转换数字
    while (*str >= '0' && *str <= '9') {
        result = result * 10 + (*str - '0');
        str++;
    }

    return result * sign;
}

/**
 * @brief 获取系统时钟节拍
 * @return 时钟节拍值
 */
uint32_t yb_protocol_get_tick(void)
{
    return HAL_GetTick();
}

/**
 * @brief UART接收中断回调函数
 * @param comm 通信结构指针
 */
void yb_protocol_uart_rx_callback(yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return;
    }

    // 重新启动接收中断
    HAL_UART_Receive_IT(comm->huart, &comm->protocol.rx_buffer[comm->protocol.rx_index], 1);
}

/**
 * @brief UART错误中断回调函数
 * @param comm 通信结构指针
 */
void yb_protocol_uart_error_callback(yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return;
    }

    // 重置接收状态
    yb_protocol_reset_rx_state(comm);

    // 重新启动接收中断
    HAL_UART_Receive_IT(comm->huart, &comm->protocol.rx_buffer[0], 1);
}

/* 私有函数实现 Private Function Implementations */

/**
 * @brief 解析协议数据
 * @param comm 通信结构指针
 * @param data_buf 数据缓冲区
 * @param length 数据长度
 * @return HAL状态
 */
static HAL_StatusTypeDef yb_protocol_parse_data(yb_uart_comm_t* comm, uint8_t* data_buf, uint16_t length)
{
    if (comm == NULL || data_buf == NULL || length < 5) { // 最小长度: $,len,id,#
        return HAL_ERROR;
    }

    // 校验协议头尾
    if (data_buf[0] != YB_PTO_HEAD || data_buf[length - 1] != YB_PTO_TAIL) {
        return HAL_ERROR;
    }

    // 分割字段
    uint8_t field_index[YB_PTO_MAX_FIELDS] = {0};
    uint8_t field_count = 1;

    for (uint16_t i = 1; i < length - 1; i++) {
        if (data_buf[i] == YB_PTO_FIELD_SEPARATOR) {
            data_buf[i] = '\0'; // 替换为字符串结束符
            field_index[field_count] = i;
            field_count++;
            if (field_count >= YB_PTO_MAX_FIELDS) {
                break;
            }
        }
    }

    if (field_count < 2) { // 至少需要长度和ID字段
        return HAL_ERROR;
    }

    // 解析长度和协议ID
    uint8_t protocol_length = yb_protocol_char_to_int((char*)&data_buf[field_index[0] + 1]);
    yb_protocol_id_t protocol_id = (yb_protocol_id_t)yb_protocol_char_to_int((char*)&data_buf[field_index[1] + 1]);

    // 查找对应的处理器
    yb_func_desc_t* handler = yb_protocol_find_handler(comm, protocol_id);
    if (handler == NULL) {
        return HAL_ERROR; // 未找到处理器
    }

    // 准备协议数据结构
    yb_protocol_data_t protocol_data = {0};
    protocol_data.protocol_id = protocol_id;
    protocol_data.field_count = handler->field_count;

    // 解析字段数据
    for (uint8_t i = 0; i < handler->field_count && i < YB_PTO_MAX_FIELDS; i++) {
        const yb_field_meta_t* meta = &handler->fields[i];
        uint8_t pos = meta->pos;

        if (pos >= field_count) {
            continue; // 字段位置超出范围
        }

        if (meta->type == YB_FIELD_STRING) {
            // 字符串字段
            uint16_t start = field_index[pos] + 1;
            uint16_t end = (pos + 1 < field_count) ? field_index[pos + 1] : (length - 1);
            uint16_t str_len = end - start;

            if (str_len >= YB_PTO_BUF_LEN_MAX) {
                str_len = YB_PTO_BUF_LEN_MAX - 1;
            }

            memcpy(protocol_data.strings[i], &data_buf[start], str_len);
            protocol_data.strings[i][str_len] = '\0';
        } else {
            // 整数字段
            protocol_data.values[i] = yb_protocol_char_to_int((char*)&data_buf[field_index[pos] + 1]);
        }
    }

    // 调用处理函数
    handler->handler(&protocol_data);

    return HAL_OK;
}

/**
 * @brief 查找协议处理器
 * @param comm 通信结构指针
 * @param id 协议ID
 * @return 处理器指针，未找到返回NULL
 */
static yb_func_desc_t* yb_protocol_find_handler(yb_uart_comm_t* comm, yb_protocol_id_t id)
{
    if (comm == NULL || !comm->initialized) {
        return NULL;
    }

    for (uint8_t i = 0; i < comm->protocol.handler_count; i++) {
        if (comm->protocol.handlers[i].id == id) {
            return &comm->protocol.handlers[i];
        }
    }

    return NULL;
}

/**
 * @brief 重置接收状态
 * @param comm 通信结构指针
 */
static void yb_protocol_reset_rx_state(yb_uart_comm_t* comm)
{
    if (comm == NULL || !comm->initialized) {
        return;
    }

    yb_protocol_ctrl_t* pto = &comm->protocol;
    pto->state = YB_PTO_STATE_IDLE;
    pto->rx_index = 0;
    pto->rx_length = 0;
    pto->new_data_flag = false;

    // 清空接收缓冲区
    memset(pto->rx_buffer, 0, YB_PTO_BUF_LEN_MAX);
}
