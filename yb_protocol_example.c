/**
 * @file yb_protocol_example.c
 * @brief YB协议HAL库使用示例
 * <AUTHOR>
 * @date 2024
 */

#include "yb_protocol_app.h"
#include "main.h"  // 包含HAL库和系统配置

/* 全局变量 Global Variables */
static yb_uart_comm_t g_uart_comm;
static yb_protocol_app_t g_protocol_app;
static UART_HandleTypeDef huart2;  // 假设使用UART2

/* 回调函数实现 Callback Function Implementations */

/**
 * @brief 颜色识别回调函数
 * @param data 颜色数据指针
 */
void on_color_detected(const yb_color_data_t* data)
{
    printf("Color detected at: x=%d, y=%d, size=%dx%d\r\n", 
           data->x, data->y, data->w, data->h);
    
    // 根据颜色位置控制机器人运动
    if (data->x < 320) {
        printf("Turn left\r\n");
        // 添加左转控制代码
    } else if (data->x > 320) {
        printf("Turn right\r\n");
        // 添加右转控制代码
    } else {
        printf("Move forward\r\n");
        // 添加前进控制代码
    }
}

/**
 * @brief 二维码识别回调函数
 * @param data 二维码数据指针
 */
void on_qrcode_detected(const yb_code_data_t* data)
{
    printf("QRCode detected: %s at (%d,%d) size=%dx%d\r\n", 
           data->message, data->x, data->y, data->w, data->h);
    
    // 根据二维码内容执行相应动作
    if (strcmp(data->message, "STOP") == 0) {
        printf("Stop command received\r\n");
        // 添加停止控制代码
    } else if (strcmp(data->message, "START") == 0) {
        printf("Start command received\r\n");
        // 添加启动控制代码
    } else if (strncmp(data->message, "SPEED:", 6) == 0) {
        int speed = atoi(data->message + 6);
        printf("Set speed to: %d\r\n", speed);
        // 添加速度控制代码
    }
}

/**
 * @brief 人脸检测回调函数
 * @param data 人脸数据指针
 */
void on_face_detected(const yb_detection_data_t* data)
{
    printf("Face detected at: x=%d, y=%d, size=%dx%d\r\n", 
           data->x, data->y, data->w, data->h);
    
    // 人脸跟踪逻辑
    int center_x = data->x + data->w / 2;
    int center_y = data->y + data->h / 2;
    
    if (center_x < 280) {
        printf("Face tracking: turn left\r\n");
    } else if (center_x > 360) {
        printf("Face tracking: turn right\r\n");
    }
    
    if (center_y < 200) {
        printf("Face tracking: look up\r\n");
    } else if (center_y > 280) {
        printf("Face tracking: look down\r\n");
    }
}

/**
 * @brief 物体检测回调函数
 * @param data 物体数据指针
 */
void on_object_detected(const yb_object_data_t* data)
{
    printf("Object detected: %s at (%d,%d) size=%dx%d\r\n", 
           data->class_name, data->x, data->y, data->w, data->h);
    
    // 根据物体类别执行不同动作
    if (strcmp(data->class_name, "person") == 0) {
        printf("Person detected - following mode\r\n");
        // 添加人体跟随代码
    } else if (strcmp(data->class_name, "obstacle") == 0) {
        printf("Obstacle detected - avoiding\r\n");
        // 添加避障代码
    } else if (strcmp(data->class_name, "ball") == 0) {
        printf("Ball detected - tracking\r\n");
        // 添加球体追踪代码
    }
}

/**
 * @brief OCR识别回调函数
 * @param text 识别的文本
 */
void on_ocr_detected(const char* text)
{
    printf("OCR text: %s\r\n", text);
    
    // 根据识别的文本执行命令
    if (strstr(text, "FORWARD") != NULL) {
        printf("OCR command: Move forward\r\n");
    } else if (strstr(text, "BACKWARD") != NULL) {
        printf("OCR command: Move backward\r\n");
    } else if (strstr(text, "LEFT") != NULL) {
        printf("OCR command: Turn left\r\n");
    } else if (strstr(text, "RIGHT") != NULL) {
        printf("OCR command: Turn right\r\n");
    }
}

/**
 * @brief 手势识别回调函数
 * @param gesture 手势名称
 */
void on_gesture_detected(const char* gesture)
{
    printf("Gesture detected: %s\r\n", gesture);
    
    // 根据手势执行相应动作
    if (strcmp(gesture, "thumbs_up") == 0) {
        printf("Thumbs up - start following\r\n");
    } else if (strcmp(gesture, "stop") == 0) {
        printf("Stop gesture - halt all movement\r\n");
    } else if (strcmp(gesture, "peace") == 0) {
        printf("Peace gesture - dance mode\r\n");
    } else if (strcmp(gesture, "fist") == 0) {
        printf("Fist gesture - defense mode\r\n");
    }
}

/* 初始化函数 Initialization Functions */

/**
 * @brief 初始化UART硬件
 * @return HAL状态
 */
HAL_StatusTypeDef init_uart_hardware(void)
{
    // 配置UART参数
    huart2.Instance = USART2;
    huart2.Init.BaudRate = 115200;
    huart2.Init.WordLength = UART_WORDLENGTH_8B;
    huart2.Init.StopBits = UART_STOPBITS_1;
    huart2.Init.Parity = UART_PARITY_NONE;
    huart2.Init.Mode = UART_MODE_TX_RX;
    huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart2.Init.OverSampling = UART_OVERSAMPLING_16;
    
    return HAL_UART_Init(&huart2);
}

/**
 * @brief 示例1：颜色追踪应用
 * @return HAL状态
 */
HAL_StatusTypeDef example_color_tracking(void)
{
    HAL_StatusTypeDef status;
    
    printf("Starting color tracking example...\r\n");
    
    // 初始化UART硬件
    status = init_uart_hardware();
    if (status != HAL_OK) {
        printf("UART init failed\r\n");
        return status;
    }
    
    // 初始化协议通信
    status = yb_protocol_init(&g_uart_comm, &huart2);
    if (status != HAL_OK) {
        printf("Protocol init failed\r\n");
        return status;
    }
    
    // 快速初始化颜色追踪
    status = yb_app_quick_init_color_tracking(&g_protocol_app, &g_uart_comm, on_color_detected);
    if (status != HAL_OK) {
        printf("Color tracking init failed\r\n");
        return status;
    }
    
    printf("Color tracking initialized successfully\r\n");
    
    // 主循环
    while (1) {
        // 处理协议数据
        yb_app_process(&g_protocol_app);
        
        // 检查连接状态
        if (!yb_app_is_connected(&g_protocol_app)) {
            printf("Connection lost\r\n");
        }
        
        // 其他应用逻辑
        HAL_Delay(10);
    }
    
    return HAL_OK;
}

/**
 * @brief 示例2：多功能检测应用
 * @return HAL状态
 */
HAL_StatusTypeDef example_multi_detection(void)
{
    HAL_StatusTypeDef status;
    
    printf("Starting multi-detection example...\r\n");
    
    // 初始化UART硬件
    status = init_uart_hardware();
    if (status != HAL_OK) {
        printf("UART init failed\r\n");
        return status;
    }
    
    // 初始化协议通信
    status = yb_protocol_init(&g_uart_comm, &huart2);
    if (status != HAL_OK) {
        printf("Protocol init failed\r\n");
        return status;
    }
    
    // 初始化应用层
    status = yb_app_init(&g_protocol_app, &g_uart_comm);
    if (status != HAL_OK) {
        printf("App init failed\r\n");
        return status;
    }
    
    // 注册多个回调函数
    yb_app_register_qrcode_callback(&g_protocol_app, on_qrcode_detected);
    yb_app_register_face_detect_callback(&g_protocol_app, on_face_detected);
    yb_app_register_object_detect_callback(&g_protocol_app, on_object_detected);
    yb_app_register_ocr_callback(&g_protocol_app, on_ocr_detected);
    yb_app_register_gesture_callback(&g_protocol_app, on_gesture_detected);
    
    printf("Multi-detection initialized successfully\r\n");
    
    // 主循环
    uint32_t last_stats_time = HAL_GetTick();
    
    while (1) {
        // 处理协议数据
        yb_app_process(&g_protocol_app);
        
        // 定期打印统计信息
        if (HAL_GetTick() - last_stats_time > 5000) {
            uint32_t rx_count, success_count, error_count;
            yb_app_get_statistics(&g_protocol_app, &rx_count, &success_count, &error_count);
            printf("Stats: RX=%lu, Success=%lu, Error=%lu\r\n", rx_count, success_count, error_count);
            last_stats_time = HAL_GetTick();
        }
        
        // 检查连接状态
        if (!yb_app_is_connected(&g_protocol_app)) {
            printf("Connection lost\r\n");
        }
        
        // 其他应用逻辑
        HAL_Delay(10);
    }
    
    return HAL_OK;
}

/* HAL库回调函数 HAL Library Callback Functions */

/**
 * @brief UART接收完成回调函数
 * @param huart UART句柄指针
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART2) {
        // 处理接收到的字节
        uint8_t rx_byte = g_uart_comm.protocol.rx_buffer[g_uart_comm.protocol.rx_index - 1];
        yb_protocol_rx_byte(&g_uart_comm, rx_byte);
        
        // 调用协议层回调
        yb_protocol_uart_rx_callback(&g_uart_comm);
    }
}

/**
 * @brief UART错误回调函数
 * @param huart UART句柄指针
 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART2) {
        yb_protocol_uart_error_callback(&g_uart_comm);
    }
}

/**
 * @brief 主函数示例
 * @return 程序退出码
 */
int main(void)
{
    // HAL库初始化
    HAL_Init();
    
    // 系统时钟配置
    SystemClock_Config();
    
    // 选择运行的示例
    #ifdef COLOR_TRACKING_EXAMPLE
        example_color_tracking();
    #else
        example_multi_detection();
    #endif
    
    return 0;
}
